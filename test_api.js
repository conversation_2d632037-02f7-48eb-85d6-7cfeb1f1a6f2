// 测试SiliconFlow API是否返回reasoning_content的简单测试
async function testSiliconFlowAPI() {
    const apiKey = 'sk-yshvlweotabrqhtjxzuxrjtaznyuxqlxktararvwkdoelqpu';
    const apiUrl = 'https://api.siliconflow.cn/v1/chat/completions';
    
    const requestBody = {
        model: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
        messages: [
            {
                role: 'system',
                content: '你是一个专业的课程助手，请帮助用户解答学习相关的问题。'
            },
            {
                role: 'user',
                content: '什么是机器学习？请简单解释一下。'
            }
        ],
        stream: true,
        max_tokens: 1000,
        temperature: 0.7
    };
    
    try {
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });
        
        if (!response.ok) {
            console.error('API请求失败:', response.status, response.statusText);
            return;
        }
        
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        
        console.log('开始处理流式响应...');
        
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';
            
            for (const line of lines) {
                if (line.trim().startsWith('data: ')) {
                    const data = line.trim().slice(6);
                    if (data === '[DONE]') {
                        console.log('流式响应结束');
                        return;
                    }
                    
                    try {
                        const chunk = JSON.parse(data);
                        console.log('收到chunk:', JSON.stringify(chunk, null, 2));
                        
                        // 检查是否有reasoning_content
                        if (chunk.choices && chunk.choices[0] && chunk.choices[0].message && chunk.choices[0].message.reasoning_content) {
                            console.log('发现reasoning_content:');
                            console.log(chunk.choices[0].message.reasoning_content);
                        }
                    } catch (e) {
                        console.log('JSON解析错误:', e, '数据:', data);
                    }
                }
            }
        }
        
    } catch (error) {
        console.error('测试失败:', error);
    }
}

// 运行测试
console.log('开始测试SiliconFlow API...');
testSiliconFlowAPI();
