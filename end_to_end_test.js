/**
 * 端到端测试脚本
 * 测试AI聊天界面的所有主要功能，包括：
 * 1. 四个模型的支持
 * 2. 网络状态指示器
 * 3. CORS代理功能
 * 4. 思考过程提取
 */

class EndToEndTester {
    constructor() {
        this.results = {};
        this.corsProxy = new CorsProxy();
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('===== 开始端到端测试 =====');
        
        try {
            // 测试网络连接
            await this.testNetworkConnectivity();
            
            // 测试CORS代理
            await this.testCorsProxy();
            
            // 测试思考过程提取
            this.testThinkingExtraction();
            
            // 测试各个模型API
            await this.testModels();
            
            console.log('\n===== 所有测试完成 =====');
            console.table(this.getTestSummary());
        } catch (error) {
            console.error('测试过程中发生错误:', error);
        }
    }
    
    /**
     * 测试网络连接
     */
    async testNetworkConnectivity() {
        console.log('\n----- 测试网络连接 -----');
        
        // 检查浏览器网络状态
        const isOnline = navigator.onLine;
        this.results.networkStatus = isOnline ? '✅ 在线' : '❌ 离线';
        console.log(`浏览器网络状态: ${this.results.networkStatus}`);
        
        // 测试各API端点连接
        const endpoints = [
            { name: 'DeepSeek API', url: 'https://api.deepseek.com' },
            { name: 'SiliconFlow API', url: 'https://api.siliconflow.cn' },
            { name: 'Qwen3-32B API', url: 'https://api-wdf2x6i7w7u3ebj8.aistudio-app.com' }
        ];
        
        for (const endpoint of endpoints) {
            try {
                console.log(`测试连接 ${endpoint.name}...`);
                const response = await fetch(`${endpoint.url}/v1/models`, { 
                    method: 'HEAD',
                    mode: 'no-cors' // 避免CORS问题
                });
                this.results[`${endpoint.name}连接`] = '✅ 成功';
                console.log(`${endpoint.name} 连接成功`);
            } catch (error) {
                this.results[`${endpoint.name}连接`] = '❓ 未知';
                console.log(`${endpoint.name} 连接测试无法确定 (no-cors模式无法获取详细结果)`);
            }
        }
    }
    
    /**
     * 测试CORS代理
     */
    async testCorsProxy() {
        console.log('\n----- 测试CORS代理 -----');
        
        const testUrl = 'https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1/models';
        console.log(`使用CORS代理请求: ${testUrl}`);
        
        try {
            // 使用代理发送请求
            const response = await this.corsProxy.fetch(testUrl, { 
                method: 'HEAD',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            this.results.corsProxy = '✅ 成功';
            console.log(`CORS代理请求成功: ${response.status} ${response.statusText}`);
            console.log(`使用的代理: ${this.corsProxy.getCurrentProxy()}`);
            
            // 测试代理轮换
            const initialProxy = this.corsProxy.getCurrentProxy();
            this.corsProxy.rotateProxy();
            const secondProxy = this.corsProxy.getCurrentProxy();
            
            this.results.proxyRotation = initialProxy !== secondProxy ? '✅ 成功' : '❌ 失败';
            console.log(`代理轮换: ${this.results.proxyRotation}`);
            console.log(`从 ${initialProxy} 切换到 ${secondProxy}`);
        } catch (error) {
            this.results.corsProxy = '❌ 失败';
            console.error(`CORS代理请求失败: ${error.message}`);
        }
    }
    
    /**
     * 测试思考过程提取
     */
    testThinkingExtraction() {
        console.log('\n----- 测试思考过程提取 -----');
        
        // 测试各种格式的思考过程
        const testCases = [
            { 
                name: 'Qwen3格式', 
                content: '这是一个复杂问题。<think>首先我需要理解问题的本质，然后再分析各个部分。这个问题涉及到数学概念。</think>问题的答案是42。' 
            },
            { 
                name: 'DeepSeek格式', 
                content: '让我分析一下。<|thinking|>首先计算总数，然后除以平均值，得到结果应该是7。</|thinking|>答案是7。' 
            },
            { 
                name: '中文标记', 
                content: '<思考>这个算法的时间复杂度是O(n log n)，因为它使用了归并排序。</思考>算法的时间复杂度是O(n log n)。' 
            },
            { 
                name: '无标记格式', 
                content: '让我思考一下这个问题：如果我们考虑能量守恒定律，那么输入的能量必须等于输出的能量加上损耗。因此，效率永远不可能达到100%。\n\n根据热力学第二定律，任何能量转换都会有损耗。' 
            }
        ];
        
        let allSucceeded = true;
        
        for (const testCase of testCases) {
            console.log(`\n测试: ${testCase.name}`);
            const result = ThinkingExtractor.extract(testCase.content);
            
            const success = result.thinking.length > 0;
            if (success) {
                console.log(`✅ 成功提取思考内容: "${result.thinking.substring(0, 30)}..."`);
            } else {
                console.log(`❌ 未能提取思考内容`);
                allSucceeded = false;
            }
        }
        
        this.results.thinkingExtraction = allSucceeded ? '✅ 成功' : '❌ 部分失败';
        console.log(`思考提取测试: ${this.results.thinkingExtraction}`);
    }
    
    /**
     * 测试模型API（模拟测试，不实际调用API）
     */
    async testModels() {
        console.log('\n----- 测试模型支持 -----');
        
        // 获取聊天应用配置
        const modelIds = ['deepseek-v3', 'deepseek-r1', 'qwen', 'qwen3-32b'];
        
        for (const modelId of modelIds) {
            try {
                // 模拟测试模型配置
                const modelConfig = this.getModelConfig(modelId);
                console.log(`测试模型: ${modelConfig.name}`);
                console.log(`  API URL: ${modelConfig.apiUrl}`);
                console.log(`  模型名称: ${modelConfig.modelName}`);
                console.log(`  提供者: ${modelConfig.provider}`);
                console.log(`  使用CORS代理: ${modelConfig.useCorsProxy ? '是' : '否'}`);
                
                this.results[`模型${modelConfig.name}`] = '✅ 配置正确';
            } catch (error) {
                this.results[`模型${modelId}`] = '❌ 配置错误';
                console.error(`模型 ${modelId} 配置错误: ${error.message}`);
            }
        }
    }
    
    /**
     * 获取模型配置（模拟聊天应用的配置）
     */
    getModelConfig(modelId) {
        const configs = {
            'deepseek-v3': {
                name: 'DeepSeek-V3',
                apiUrl: 'https://api.deepseek.com/chat/completions',
                modelName: 'deepseek-chat',
                provider: 'deepseek',
                useCorsProxy: false
            },
            'deepseek-r1': {
                name: 'DeepSeek-R1',
                apiUrl: 'https://api.deepseek.com/chat/completions',
                modelName: 'deepseek-reasoner',
                provider: 'deepseek',
                useCorsProxy: false
            },
            'qwen': {
                name: 'DeepSeek-R1-Qwen-7B',
                apiUrl: 'https://api.siliconflow.cn/v1/chat/completions',
                modelName: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
                provider: 'siliconflow',
                useCorsProxy: false
            },
            'qwen3-32b': {
                name: 'Qwen3-32B',
                apiUrl: 'https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1/chat/completions',
                modelName: 'qwen3:32b',
                provider: 'qwen3',
                useCorsProxy: true
            }
        };
        
        const config = configs[modelId];
        if (!config) {
            throw new Error(`未知模型ID: ${modelId}`);
        }
        return config;
    }
    
    /**
     * 获取测试摘要
     */
    getTestSummary() {
        return Object.entries(this.results).map(([test, result]) => {
            return { 测试项目: test, 结果: result };
        });
    }
}

// 检查是否在浏览器环境
if (typeof window !== 'undefined') {
    // 浏览器环境
    window.EndToEndTester = EndToEndTester;
    window.runE2ETests = function() {
        const tester = new EndToEndTester();
        return tester.runAllTests();
    };
    console.log('端到端测试已加载。使用 window.runE2ETests() 开始测试。');
} else {
    console.log('此测试脚本需要在浏览器环境中运行。');
}
