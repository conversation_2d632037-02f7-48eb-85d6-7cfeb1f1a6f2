{"apiKeys": {"deepseek": "***********************************", "siliconflow": "sk-yshvlweotabrqhtjxzuxrjtaznyuxqlxktararvwkdoelqpu", "qwen3": "0621faa5641beacc5940d0fb978114a03a69b7eb"}, "models": {"deepseek-v3": {"name": "DeepSeek-V3", "api_url": "https://api.deepseek.com/chat/completions", "model_name": "deepseek-chat", "provider": "deepseek"}, "deepseek-r1": {"name": "DeepSeek-R1", "api_url": "https://api.deepseek.com/chat/completions", "model_name": "deepseek-reasoner", "provider": "deepseek"}, "qwen": {"name": "DeepSeek-R1-<PERSON><PERSON>-7B", "api_url": "https://api.siliconflow.cn/v1/chat/completions", "model_name": "deepseek-ai/DeepSeek-R1-<PERSON>still-Qwen-7B", "provider": "siliconflow"}, "qwen3-32b": {"name": "Qwen3-32B", "api_url": "https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1/chat/completions", "model_name": "qwen3:32b", "provider": "qwen3-32b"}}, "default_settings": {"max_tokens": 2048, "temperature": 0.7, "top_p": 0.9, "system_message": "你是一个专业的课程助手，请帮助用户解答学习相关的问题。"}, "usage_instructions": {"step1": "将您的DeepSeek API Key替换 'your_deepseek_api_key_here'", "step2": "将您的硅基流动 API Key替换 'your_siliconflow_api_key_here'", "step3": "将您的Qwen3-32B API Key替换 'your_qwen3_api_key_here'", "step4": "打开 ai-chat.html 文件", "step5": "点击右上角齿轮图标配置API密钥", "step6": "选择想要使用的AI模型", "step7": "开始与AI对话"}, "api_endpoints": {"deepseek": {"base_url": "https://api.deepseek.com", "chat_completions": "https://api.deepseek.com/chat/completions", "documentation": "https://platform.deepseek.com/docs"}, "siliconflow": {"base_url": "https://api.siliconflow.cn", "chat_completions": "https://api.siliconflow.cn/v1/chat/completions", "documentation": "https://siliconflow.cn/docs"}, "qwen3-32b": {"base_url": "https://api-wdf2x6i7w7u3ebj8.aistudio-app.com", "chat_completions": "https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1/chat/completions", "documentation": "https://aistudio.baidu.com/docs"}}}