/**
 * 功能测试脚本 - 测试新实现的功能
 * 1. 测试CORS代理功能（特别是对Qwen3-32B API的支持）
 * 2. 测试网络状态指示器
 * 3. 测试思考过程提取功能
 */

// 测试CORS代理功能
async function testCorsProxy() {
    console.log('=== 测试CORS代理功能 ===');
    const corsProxy = new CorsProxy();
    
    // 测试API端点
    const testUrls = [
        'https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1/chat/completions', // Qwen3-32B API
        'https://api.deepseek.com/v1/models' // DeepSeek API
    ];
    
    for (const url of testUrls) {
        console.log(`\n测试URL: ${url}`);
        try {
            // 仅测试HEAD请求以避免API消耗
            const response = await corsProxy.fetch(url, { 
                method: 'HEAD',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            console.log(`状态: ${response.status} ${response.statusText}`);
            console.log(`代理: ${corsProxy.getCurrentProxy()}`);
            console.log('测试成功 ✅');
        } catch (error) {
            console.error(`测试失败 ❌: ${error.message}`);
            console.log(`当前代理: ${corsProxy.getCurrentProxy()}`);
        }
    }
    
    // 测试代理轮换
    console.log('\n测试代理轮换功能:');
    const initialProxy = corsProxy.getCurrentProxy();
    console.log(`初始代理: ${initialProxy}`);
    
    corsProxy.rotateProxy();
    const secondProxy = corsProxy.getCurrentProxy();
    console.log(`轮换后代理: ${secondProxy}`);
    
    corsProxy.rotateProxy();
    const thirdProxy = corsProxy.getCurrentProxy();
    console.log(`再次轮换后代理: ${thirdProxy}`);
    
    // 重置回第一个代理
    while (corsProxy.getCurrentProxy() !== initialProxy) {
        corsProxy.rotateProxy();
    }
    console.log(`重置回初始代理: ${corsProxy.getCurrentProxy()}`);
}

// 测试思考过程提取功能
function testThinkingExtractor() {
    console.log('\n=== 测试思考过程提取功能 ===');
    
    // 测试不同格式的思考过程
    const testCases = [
        {
            name: 'Qwen3-32B格式',
            content: '让我思考一下这个问题。<think>机器学习是人工智能的一个子领域，主要关注如何让计算机从数据中学习。它使用统计学方法使计算机在没有被明确编程的情况下进行预测或决策。</think>机器学习是一种让计算机能够从数据中学习并做出决策的方法，无需显式编程。'
        },
        {
            name: 'DeepSeek-R1格式',
            content: '让我分析一下。<|thinking|>机器学习有三种主要类型：监督学习、无监督学习和强化学习。监督学习使用标记数据，无监督学习处理未标记数据，强化学习通过与环境交互来学习。</|thinking|>机器学习主要分为三类：监督学习、无监督学习和强化学习。'
        },
        {
            name: 'ChatGLM风格',
            content: '<思考>深度学习是机器学习的一个分支，基于人工神经网络。它使用多层神经网络来模拟人脑的学习过程，可以处理更复杂的模式识别任务。</思考>深度学习是机器学习的一个分支，使用多层神经网络模拟人脑学习过程。'
        },
        {
            name: '自定义标记格式',
            content: '[思考开始]卷积神经网络(CNN)主要用于图像处理，而循环神经网络(RNN)更适合序列数据如文本和语音。[思考结束]不同类型的神经网络适用于不同类型的数据。'
        },
        {
            name: '无标记的思考内容',
            content: '让我思考一下这个问题：决策树是一种基于树状结构进行决策的监督学习算法。它通过一系列问题将数据集分割成越来越小的子集，直到每个叶节点对应一个类别。\n\n决策树是一种直观的机器学习算法，通过问题序列将数据分类。'
        }
    ];
    
    for (const testCase of testCases) {
        console.log(`\n测试: ${testCase.name}`);
        const result = ThinkingExtractor.extract(testCase.content);
        
        console.log(`思考内容长度: ${result.thinking.length}`);
        console.log(`回答内容长度: ${result.answer.length}`);
        
        if (result.thinking) {
            console.log(`思考内容预览: ${result.thinking.substring(0, 50)}...`);
            console.log('测试成功 ✅');
        } else {
            console.log('未提取到思考内容 ❌');
        }
    }
}

// 网络状态监测测试（模拟离线/在线切换）
function testNetworkStatus() {
    console.log('\n=== 测试网络状态监测 ===');
    console.log('由于浏览器安全限制，此功能需要在HTML页面中测试');
    console.log('请打开ai-chat.html文件，并在开发者工具的网络面板中切换"离线"模式来测试');
    console.log('提示: 按F12打开开发者工具，找到网络(Network)面板，勾选/取消"离线"模式');
}

// 运行测试
async function runAllTests() {
    console.log('开始测试新实现的功能...\n');
    
    try {
        await testCorsProxy();
        testThinkingExtractor();
        testNetworkStatus();
        
        console.log('\n=== 所有测试完成 ===');
    } catch (error) {
        console.error('测试过程中发生错误:', error);
    }
}

// 检查是否在浏览器环境
if (typeof window !== 'undefined') {
    // 浏览器环境
    window.runTests = runAllTests;
    console.log('测试脚本已加载。请调用window.runTests()函数开始测试。');
} else {
    // Node.js环境
    console.log('此测试脚本需要在浏览器环境中运行，因为它依赖于浏览器的Web API。');
}
