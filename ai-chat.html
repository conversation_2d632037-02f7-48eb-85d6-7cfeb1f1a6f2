<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">    <title>AI 课程助手 - 智能对话</title>
    <!-- Markdown渲染库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- 代码高亮库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .chat-container {
            width: 100%;
            max-width: 900px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .model-selector {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 15px;
        }

        .model-btn {
            padding: 8px 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .model-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .model-btn.active {
            background: rgba(255, 255, 255, 0.3);
            border-color: white;
        }

        .config-btn {
            position: absolute;
            right: 20px;
            top: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .config-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(90deg);
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            display: flex;
            margin-bottom: 15px;
            animation: fadeIn 0.5s ease;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.assistant {
            justify-content: flex-start;
        }        .message-content {
            max-width: 70%;
            padding: 12px 18px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 5px;
        }        .message.assistant .message-content {
            background: #f1f3f4;
            color: #333;
            border-bottom-left-radius: 5px;
            white-space: normal;
        }

        /* Markdown样式 */
        .message-content h1, .message-content h2, .message-content h3,
        .message-content h4, .message-content h5, .message-content h6 {
            margin: 12px 0 8px 0;
            font-weight: bold;
        }

        .message-content h1 { font-size: 1.4em; }
        .message-content h2 { font-size: 1.3em; }
        .message-content h3 { font-size: 1.2em; }
        .message-content h4 { font-size: 1.1em; }

        .message-content p {
            margin: 8px 0;
        }

        .message-content ul, .message-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .message-content li {
            margin: 4px 0;
        }

        .message-content blockquote {
            border-left: 4px solid #667eea;
            padding-left: 12px;
            margin: 8px 0;
            color: #666;
            font-style: italic;
        }

        .message-content code {
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: #d63384;
        }

        .message-content pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .message-content pre code {
            background: none;
            padding: 0;
            color: inherit;
        }

        .message-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 8px 0;
        }

        .message-content th, .message-content td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .message-content th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .message-content strong {
            font-weight: bold;
        }

        .message-content em {
            font-style: italic;
        }

        .message-content a {
            color: #667eea;
            text-decoration: none;
        }        .message-content a:hover {
            text-decoration: underline;
        }        /* 打字光标动画 */
        .typing-cursor {
            color: #667eea;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }        /* 思考过程展示样式 */
        .thinking-section {
            margin-bottom: 16px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
            overflow: hidden;
        }

        .thinking-header {
            padding: 12px 16px;
            background: linear-gradient(90deg, #f1f3f4 0%, #f8f9fa 100%);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 500;
            color: #5f6368;
            border-bottom: 1px solid #e1e5e9;
            transition: all 0.2s ease;
            user-select: none;
        }

        .thinking-header:hover {
            background: linear-gradient(90deg, #e8eaed 0%, #f1f3f4 100%);
        }

        .thinking-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .thinking-icon {
            font-size: 16px;
            animation: thinking-pulse 2s ease-in-out infinite;
        }

        @keyframes thinking-pulse {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.1); opacity: 1; }
        }

        .thinking-toggle {
            font-size: 12px;
            color: #9aa0a6;
            transition: transform 0.2s ease;
            font-family: monospace;
        }

        .thinking-toggle.expanded {
            transform: rotate(180deg);
        }

        .thinking-content {
            padding: 0;
            background: white;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 13px;
            line-height: 1.6;
            color: #374151;
            white-space: pre-wrap;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
            border-radius: 0 0 7px 7px;
        }

        .thinking-content.expanded {
            padding: 16px 20px;
            max-height: 600px;
            overflow-y: auto;
            border-top: 1px solid #f0f0f0;
        }

        .thinking-content::-webkit-scrollbar {
            width: 6px;
        }

        .thinking-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .thinking-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .thinking-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .answer-section {
            margin-top: 8px;
        }

        /* 为R1模型的响应添加特殊标识 */
        .r1-response {
            position: relative;
        }

        .r1-response::before {
            content: "R1";
            position: absolute;
            top: -8px;
            right: 8px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: bold;
            font-family: monospace;
        }

        .chat-input-container {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }

        .chat-input {
            display: flex;
            gap: 15px;
            align-items: flex-end;
        }

        .input-field {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            resize: none;
            min-height: 50px;
            max-height: 120px;
            transition: border-color 0.3s ease;
        }

        .input-field:focus {
            outline: none;
            border-color: #667eea;
        }

        .send-btn {
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            min-width: 80px;
        }

        .send-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .loading {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #666;
            font-style: italic;
        }

        .loading-dots {
            display: inline-flex;
            gap: 3px;
        }

        .loading-dot {
            width: 6px;
            height: 6px;
            background: #667eea;
            border-radius: 50%;
            animation: bounce 1.4s infinite ease-in-out both;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes bounce {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .config-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .config-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .config-header {
            margin-bottom: 20px;
            text-align: center;
        }

        .config-group {
            margin-bottom: 20px;
        }

        .config-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .config-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
        }

        .config-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .config-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .config-btn-save, .config-btn-cancel {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
        }

        .config-btn-save {
            background: #667eea;
            color: white;
        }

        .config-btn-cancel {
            background: #6c757d;
            color: white;
        }

        .error-message {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }        @media (max-width: 768px) {
            .chat-container {
                height: 95vh;
                margin: 10px;
            }
            
            .model-selector {
                flex-direction: column;
                gap: 5px;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
        
        /* 网络状态指示器 */
        .status-indicator {
            position: absolute;
            left: 20px;
            top: 20px;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            background: rgba(0, 0, 0, 0.15);
            padding: 4px 8px;
            border-radius: 12px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #4CAF50; /* 在线状态 */
        }
        
        .status-dot.offline {
            background-color: #F44336; /* 离线状态 */
        }
        
        .status-dot.connecting {
            background-color: #FFC107; /* 连接中状态 */
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.3; }
            100% { opacity: 1; }
        }
        
        /* 网络状态指示器 */
        .status-indicator {
            position: absolute;
            left: 20px;
            top: 20px;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            background: rgba(0, 0, 0, 0.15);
            padding: 4px 8px;
            border-radius: 12px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #4CAF50; /* 默认在线状态 */
        }

        .status-dot.online {
            background-color: #4CAF50; /* 在线状态 */
        }

        .status-dot.offline {
            background-color: #F44336; /* 离线状态 */
        }

        .status-dot.connecting {
            background-color: #FFC107; /* 连接中状态 */
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.3; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>    <div class="chat-container">        <div class="chat-header">
            <button class="config-btn" onclick="openConfig()">⚙️</button>
            <!-- 测试按钮 -->
            <button class="config-btn" onclick="showTestThinkingProcess()" title="测试思考过程显示" style="right: 100px; font-size: 14px;">
                🧪
            </button>
            <!-- API测试按钮 -->
            <button class="config-btn" onclick="testCurrentApiConnection()" title="测试当前模型API连接" style="right: 60px; font-size: 14px;">
                🔗
            </button>
            <!-- 网络状态指示器 -->
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">在线</span>
            </div>
            <h1>AI 课程助手</h1>
            <p>选择您喜欢的AI模型开始对话</p>            <div class="model-selector">
                <button class="model-btn active" data-model="deepseek-v3">DeepSeek-V3</button>
                <button class="model-btn" data-model="deepseek-r1">DeepSeek-R1</button>
                <button class="model-btn" data-model="qwen">DeepSeek-R1-Qwen-7B</button>
                <button class="model-btn" data-model="qwen3-32b">Qwen3-32B</button>
            </div>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-content">
                    您好！我是您的AI课程助手，可以帮助您解答学习中的各种问题。请选择上方的模型并开始对话吧！
                </div>
            </div>
        </div>

        <div class="chat-input-container">
            <div class="chat-input">
                <textarea 
                    class="input-field" 
                    id="messageInput" 
                    placeholder="请输入您的问题..."
                    rows="1"
                    onkeydown="handleKeyDown(event)"
                ></textarea>
                <button class="send-btn" id="sendBtn" onclick="sendMessage()">发送</button>
            </div>
        </div>
    </div>

    <!-- 配置模态框 -->
    <div class="config-modal" id="configModal">
        <div class="config-content">
            <div class="config-header">
                <h2>API 配置</h2>
                <p>请配置您的API密钥</p>
            </div>
              <div class="config-group">
                <label for="deepseekApiKey">DeepSeek API Key:</label>
                <input type="password" id="deepseekApiKey" placeholder="输入您的DeepSeek API Key">
            </div>
            
            <div class="config-group">
                <label for="siliconflowApiKey">硅基流动 API Key:</label>
                <input type="password" id="siliconflowApiKey" placeholder="输入您的硅基流动 API Key">
            </div>
            
            <div class="config-group">
                <label for="qwen3ApiKey">Qwen3-32B API Key:</label>
                <input type="password" id="qwen3ApiKey" placeholder="输入您的Qwen3-32B API Key">
            </div>
            
            <div class="config-buttons">
                <button class="config-btn-cancel" onclick="closeConfig()">取消</button>
                <button class="config-btn-save" onclick="saveConfig()">保存</button>
            </div>
        </div>
    </div>    <!-- 自定义工具类 -->
    <script src="ai-utils.js"></script>
    <!-- 主应用逻辑 -->
    <script src="ai-chat.js"></script>
    <script>
        // 全局函数（供HTML事件调用）
        function openConfig() {
            window.app.openConfig();
        }
        
        function closeConfig() {
            window.app.closeConfig();
        }
        
        function saveConfig() {
            window.app.saveConfig();
        }
        
        function sendMessage() {
            window.app.sendMessage();
        }
        
        function showTestThinkingProcess() {
            window.app.showTestThinkingProcess();
        }
        
        function toggleThinking(messageId) {
            window.app.toggleThinking(messageId);
        }

        function testCurrentApiConnection() {
            window.app.testApiConnection();
        }// 加载配置
        function loadConfig() {
            const config = localStorage.getItem('aiChatConfig');
            if (config) {
                try {
                    const parsedConfig = JSON.parse(config);
                    apiKeys = parsedConfig.apiKeys || apiKeys;
                    
                    // 填充配置表单
                    document.getElementById('deepseekApiKey').value = apiKeys.deepseek || '';
                    document.getElementById('siliconflowApiKey').value = apiKeys.siliconflow || '';
                    document.getElementById('qwen3ApiKey').value = apiKeys.qwen3 || '';
                } catch (e) {
                    console.error('配置加载失败:', e);
                }
            }
        }        // 保存配置
        function saveConfig() {
            apiKeys.deepseek = document.getElementById('deepseekApiKey').value.trim();
            apiKeys.siliconflow = document.getElementById('siliconflowApiKey').value.trim();
            apiKeys.qwen3 = document.getElementById('qwen3ApiKey').value.trim();
            
            const config = {
                apiKeys: apiKeys
            };
            
            localStorage.setItem('aiChatConfig', JSON.stringify(config));
            closeConfig();
            
            // 显示保存成功消息
            showMessage('配置已保存！', 'assistant');
        }

        // 设置模型选择器
        function setupModelSelector() {
            const modelBtns = document.querySelectorAll('.model-btn');
            modelBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    modelBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentModel = this.getAttribute('data-model');
                });
            });
        }

        // 设置文本框自动调整高度
        function setupTextareaAutoResize() {
            const textarea = document.getElementById('messageInput');
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
        }

        // 处理键盘事件
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 打开配置
        function openConfig() {
            document.getElementById('configModal').style.display = 'flex';
        }

        // 关闭配置
        function closeConfig() {
            document.getElementById('configModal').style.display = 'none';
        }        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 检查API密钥
            if (!checkApiKey()) {
                showMessage('请先配置API密钥！点击右上角⚙️进行配置。', 'assistant');
                return;
            }
            
            // 显示用户消息
            showMessage(message, 'user');
            input.value = '';
            input.style.height = 'auto';
            
            // 禁用发送按钮
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            sendBtn.textContent = '发送中...';
            
            // 创建流式响应消息容器
            const messageId = 'stream-' + Date.now();
            const streamDiv = createStreamMessage(messageId);
            
            try {
                await callAPIStream(message, messageId);
            } catch (error) {
                updateStreamMessage(messageId, `抱歉，发生了错误：${error.message}`);
            } finally {
                sendBtn.disabled = false;
                sendBtn.textContent = '发送';
            }
        }        // 检查API密钥
        function checkApiKey() {
            if (currentModel === 'qwen') {
                return apiKeys.siliconflow && apiKeys.siliconflow.length > 0;
            } else if (currentModel === 'qwen3-32b') {
                return apiKeys.qwen3 && apiKeys.qwen3.length > 0;
            } else {
                return apiKeys.deepseek && apiKeys.deepseek.length > 0;
            }
        }// 测试思考过程显示（开发用）
        function showTestThinkingProcess() {
            const messageId = 'test-' + Date.now();
            createStreamMessage(messageId);
            
            // 模拟 SiliconFlow API 返回的思考过程数据
            const testReasoning = `我需要回答关于机器学习的问题。让我仔细思考一下：

1. 首先，我需要理解用户的问题层次
   - 这可能是一个基础概念问题
   - 用户可能需要通俗易懂的解释
   - 应该包含实际应用案例

2. 机器学习的核心要素：
   - 数据：机器学习的基础
   - 算法：处理数据的方法
   - 模型：从数据中学到的规律
   - 预测：应用模型解决新问题

3. 解释策略：
   - 用类比的方式帮助理解
   - 提供具体的实际应用例子
   - 避免过于技术性的术语
   - 结构化地组织信息

4. 应该涵盖的内容：
   - 定义和核心概念
   - 与传统编程的区别
   - 主要类型（监督、无监督、强化学习）
   - 实际应用场景
   - 简单的代码示例

让我现在组织一个清晰、易懂的回答。`;
            
            const testAnswer = `# 什么是机器学习？

**机器学习**（Machine Learning）是人工智能的一个重要分支，它让计算机能够从数据中自动学习规律和模式，并利用这些规律对新数据进行预测或决策，而无需人工明确编程每一个步骤。

## 🔍 核心理念

想象一下教小孩识别动物的过程：
- **传统编程**：我们告诉计算机"如果有四条腿、会汪汪叫，那就是狗"
- **机器学习**：我们给计算机看很多狗和猫的照片，让它自己总结规律

## 📚 主要类型

### 1. 监督学习 (Supervised Learning)
- 有"标准答案"的学习
- 例如：垃圾邮件分类、房价预测

### 2. 无监督学习 (Unsupervised Learning)  
- 自己发现数据中的规律
- 例如：客户群体分析、异常检测

### 3. 强化学习 (Reinforcement Learning)
- 通过试错和奖惩学习
- 例如：游戏AI、自动驾驶

## 🚀 实际应用

- 📧 **智能邮箱**：自动过滤垃圾邮件
- 🎵 **音乐推荐**：Spotify 的个性化播放列表  
- 🛒 **电商推荐**：Amazon 的"你可能喜欢"
- 🏥 **医疗诊断**：辅助医生分析医学影像
- 🚗 **自动驾驶**：特斯拉的自动驾驶系统
- 💬 **智能助手**：Siri、小爱同学

## 💻 简单示例

\`\`\`python
# 使用机器学习预测房价
from sklearn.linear_model import LinearRegression
import numpy as np

# 训练数据：房屋面积 -> 价格
X = np.array([[50], [80], [120], [150]])  # 面积(平米)
y = np.array([30, 50, 75, 95])           # 价格(万元)

# 创建和训练模型
model = LinearRegression()
model.fit(X, y)

# 预测100平米房子的价格
predicted_price = model.predict([[100]])
print(f"预测100平米房子价格: {predicted_price[0]:.1f}万元")
\`\`\`

## 🎯 为什么重要？

机器学习正在改变我们的生活方式，从个人助手到医疗诊断，从金融风控到交通出行。它让计算机具备了"学习"的能力，这是迈向真正智能系统的重要一步。

希望这个解释能帮助您理解机器学习的基本概念！有任何具体问题欢迎继续询问。`;
            
            // 使用新的流式显示函数
            setTimeout(() => {
                simulateThinkingStream(messageId, testReasoning, testAnswer);
            }, 500);
        }        // 流式调用API        async function callAPIStream(message, messageId) {
            let apiUrl, headers, body;
            
            if (currentModel === 'qwen') {
                // 硅基流动API
                apiUrl = 'https://api.siliconflow.cn/v1/chat/completions';
                headers = {
                    'Authorization': `Bearer ${apiKeys.siliconflow}`,
                    'Content-Type': 'application/json'
                };
                body = {
                    model: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
                    messages: [
                        {
                            role: 'system',
                            content: '你是一个专业的课程助手，请帮助用户解答学习相关的问题。'
                        },
                        {
                            role: 'user',
                            content: message
                        }
                    ],
                    stream: true,
                    max_tokens: 2048,
                    temperature: 0.7,
                    top_p: 0.7,
                    top_k: 50,
                    frequency_penalty: 0.5
                };            } else if (currentModel === 'qwen3-32b') {
                // Qwen3-32B API - 启用JSONP方式调用API以解决CORS问题
                apiUrl = 'https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1/chat/completions';
                headers = {
                    'Authorization': `Bearer ${apiKeys.qwen3}`,
                    'Content-Type': 'application/json',
                    'Origin': window.location.origin // 添加源头信息
                };
                body = {
                    model: 'qwen3:32b', // 正确的模型名称，务必使用小写和冒号
                    messages: [
                        {
                            role: 'system',
                            content: '你是一个专业的课程助手，请帮助用户解答学习相关的问题。'
                        },
                        {
                            role: 'user',
                            content: message
                        }
                    ],
                    stream: true,
                    max_tokens: 2048,
                    temperature: 0.6
                };
            } else {
                // DeepSeek API
                apiUrl = 'https://api.deepseek.com/chat/completions';
                headers = {
                    'Authorization': `Bearer ${apiKeys.deepseek}`,
                    'Content-Type': 'application/json'
                };
                
                const modelName = currentModel === 'deepseek-v3' ? 'deepseek-chat' : 'deepseek-reasoner';
                
                body = {
                    model: modelName,
                    messages: [
                        {
                            role: 'system',
                            content: '你是一个专业的课程助手，请帮助用户解答学习相关的问题。'
                        },
                        {
                            role: 'user',
                            content: message
                        }
                    ],
                    stream: true,
                    max_tokens: 2048,
                    temperature: 0.7
                };
            }// 通过添加错误处理器
            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(body),
                    mode: 'cors', // 显式设置CORS模式
                    credentials: 'same-origin'
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error(`API错误 (${response.status}):`, errorText);
                    console.error('API请求详情:', {
                        url: apiUrl,
                        model: currentModel,
                        headers: {...headers, Authorization: '(已隐藏)'}
                    });
                    throw new Error(`API请求失败 (${response.status}): ${errorText}`);
                }
                
                // 判断是否为R1模型或支持思考过程的模型
            const isR1Model = currentModel === 'deepseek-r1' || currentModel === 'qwen' || currentModel === 'qwen3-32b';
            
            // 处理流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';
            let fullContent = '';
            let reasoningContent = '';
            let answerContent = '';
            let hasDisplayedThinking = false; // 标记是否已显示思考过程
            
            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';
                    
                    for (const line of lines) {
                        if (line.trim().startsWith('data: ')) {
                            const data = line.trim().slice(6);
                            if (data === '[DONE]') {
                                if (isR1Model && (reasoningContent || answerContent)) {
                                    updateStreamMessageWithReasoning(messageId, reasoningContent, answerContent, true);
                                } else {
                                    updateStreamMessage(messageId, fullContent, true);
                                }
                                return;
                            }
                            
                            try {
                                const chunk = JSON.parse(data);
                                  // 处理流式delta响应
                                if (chunk.choices && chunk.choices[0] && chunk.choices[0].delta) {
                                    const delta = chunk.choices[0].delta;
                                    
                                    // 检查是否有reasoning_content字段（Qwen3可能支持）
                                    if (delta.reasoning_content) {
                                        reasoningContent += delta.reasoning_content;
                                        updateStreamMessageWithReasoning(messageId, reasoningContent, answerContent);
                                    }
                                    // 检查是否有reasoning字段（DeepSeek R1流式）
                                    else if (delta.reasoning) {
                                        reasoningContent += delta.reasoning;
                                        updateStreamMessageWithReasoning(messageId, reasoningContent, answerContent);
                                    }                                    // 检查是否有content字段
                                    else if (delta.content) {
                                        if (isR1Model) {
                                            answerContent += delta.content;
                                            
                                            // 检查是否有<think>标签（Qwen3-32B格式）
                                            const thinkMatch = answerContent.match(/<think>([\s\S]*?)<\/think>/);
                                            if (thinkMatch && !hasDisplayedThinking) {
                                                reasoningContent = thinkMatch[1].trim();
                                                answerContent = answerContent.replace(/<think>[\s\S]*?<\/think>/, '').trim();
                                                
                                                // 模拟流式显示思考过程
                                                simulateThinkingStream(messageId, reasoningContent, answerContent);
                                                hasDisplayedThinking = true;
                                                return;
                                            }
                                            
                                            updateStreamMessageWithReasoning(messageId, reasoningContent, answerContent);
                                        } else {
                                            fullContent += delta.content;
                                            updateStreamMessage(messageId, fullContent);
                                        }
                                    }
                                }
                                // 处理完整的choice消息（SiliconFlow格式）
                                else if (chunk.choices && chunk.choices[0] && chunk.choices[0].message) {
                                    const message = chunk.choices[0].message;
                                    
                                    // 硅基流动的R1模型：reasoning_content包含完整的思考过程
                                    if (isR1Model && message.reasoning_content && !hasDisplayedThinking) {
                                        reasoningContent = message.reasoning_content;
                                        answerContent = message.content || '';
                                        
                                        // 模拟流式显示思考过程
                                        simulateThinkingStream(messageId, reasoningContent, answerContent);
                                        hasDisplayedThinking = true;
                                        return;
                                    }                                    // 普通响应或无思考过程的R1响应
                                    else if (message.content) {
                                        if (isR1Model) {
                                            let content = message.content;
                                            
                                            // 检查是否有<think>标签（Qwen3-32B格式）
                                            const thinkMatch = content.match(/<think>([\s\S]*?)<\/think>/);
                                            if (thinkMatch && !hasDisplayedThinking) {
                                                reasoningContent = thinkMatch[1].trim();
                                                answerContent = content.replace(/<think>[\s\S]*?<\/think>/, '').trim();
                                                
                                                // 模拟流式显示思考过程
                                                simulateThinkingStream(messageId, reasoningContent, answerContent);
                                                hasDisplayedThinking = true;
                                                return;
                                            } else {
                                                answerContent = content;
                                                updateStreamMessageWithReasoning(messageId, reasoningContent, answerContent, true);
                                            }
                                        } else {
                                            fullContent = message.content;
                                            updateStreamMessage(messageId, fullContent, true);
                                        }
                                        return;
                                    }
                                }
                            } catch (e) {
                                console.log('JSON parse error:', e, 'Data:', data);
                            }
                        }
                    }
                }
                  // 确保最终渲染
                if (isR1Model && (reasoningContent || answerContent)) {
                    // 最终检查是否有未处理的<think>标签
                    if (!hasDisplayedThinking && answerContent.includes('<think>')) {
                        const thinkMatch = answerContent.match(/<think>([\s\S]*?)<\/think>/);
                        if (thinkMatch) {
                            reasoningContent = thinkMatch[1].trim();
                            answerContent = answerContent.replace(/<think>[\s\S]*?<\/think>/, '').trim();
                            simulateThinkingStream(messageId, reasoningContent, answerContent);
                            return;
                        }
                    }
                    updateStreamMessageWithReasoning(messageId, reasoningContent, answerContent, true);
                } else {
                    updateStreamMessage(messageId, fullContent, true);
                }
                
            } finally {
                reader.releaseLock();
            }
        }

        // 模拟思考过程的流式显示
        function simulateThinkingStream(messageId, fullReasoning, finalAnswer) {
            if (!fullReasoning) {
                // 如果没有思考过程，直接显示答案
                updateStreamMessageWithReasoning(messageId, '', finalAnswer, true);
                return;
            }
            
            let currentReasoning = '';
            let currentAnswer = '';
            const reasoningChars = fullReasoning.split('');
            const answerChars = finalAnswer.split('');
            
            // 首先显示空的思考框架，并自动展开
            updateStreamMessageWithReasoning(messageId, '', '');
            setTimeout(() => {
                const thinkingContent = document.getElementById(`${messageId}-thinking`);
                const toggle = document.getElementById(`${messageId}-toggle`);
                if (thinkingContent && toggle) {
                    thinkingContent.classList.add('expanded');
                    toggle.classList.add('expanded');
                    toggle.textContent = '▲';
                }
            }, 100);
            
            // 模拟思考过程的打字效果
            let reasoningIndex = 0;
            const thinkingInterval = setInterval(() => {
                if (reasoningIndex < reasoningChars.length) {
                    currentReasoning += reasoningChars[reasoningIndex];
                    reasoningIndex++;
                    updateStreamMessageWithReasoning(messageId, currentReasoning, currentAnswer);
                } else {
                    clearInterval(thinkingInterval);
                    
                    // 思考过程完成后，开始显示答案
                    setTimeout(() => {
                        let answerIndex = 0;
                        const answerInterval = setInterval(() => {
                            if (answerIndex < answerChars.length) {
                                currentAnswer += answerChars[answerIndex];
                                answerIndex++;
                                updateStreamMessageWithReasoning(messageId, currentReasoning, currentAnswer);
                            } else {
                                clearInterval(answerInterval);
                                // 最终渲染
                                updateStreamMessageWithReasoning(messageId, currentReasoning, currentAnswer, true);
                            }
                        }, 20); // 答案显示速度稍快
                    }, 500); // 思考完成后稍作停顿
                }
            }, 30); // 思考过程显示速度
        }

        // 解析思考过程内容
        function parseThinkingContent(content) {
            let thinking = '';
            let answer = '';
            
            // 尝试多种思考过程标记格式
            const thinkingPatterns = [
                /<think>([\s\S]*?)<\/think>/g,
                /<\|thinking\|>([\s\S]*?)<\|\/thinking\|>/g,
                /\[思考开始\]([\s\S]*?)\[思考结束\]/g,
                /\[Thinking\]([\s\S]*?)\[\/Thinking\]/g
            ];
            
            let hasThinking = false;
            let processedContent = content;
            
            for (const pattern of thinkingPatterns) {
                const matches = [...content.matchAll(pattern)];
                if (matches.length > 0) {
                    hasThinking = true;
                    matches.forEach(match => {
                        thinking += match[1].trim() + '\n\n';
                        processedContent = processedContent.replace(match[0], '');
                    });
                    break;
                }
            }
            
            answer = processedContent.trim();
            
            return {
                thinking: thinking.trim(),
                answer: answer
            };
        }// 创建流式消息容器
        function createStreamMessage(messageId) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message assistant';
            messageDiv.id = messageId;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.innerHTML = '<span class="typing-cursor">▋</span>';
            
            messageDiv.appendChild(contentDiv);
            messagesContainer.appendChild(messageDiv);
            
            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            return messageDiv;
        }        // 更新带思考过程的流式消息内容
        function updateStreamMessageWithReasoning(messageId, reasoningContent, answerContent, isFinal = false) {
            const messageElement = document.getElementById(messageId);
            if (!messageElement) return;
            
            const contentDiv = messageElement.querySelector('.message-content');
            if (!contentDiv) return;
            
            // 为R1响应添加特殊样式类
            contentDiv.classList.add('r1-response');
            
            let html = '';
            
            // 如果有思考过程，添加思考部分
            if (reasoningContent) {
                html += `
                    <div class="thinking-section">
                        <div class="thinking-header" onclick="toggleThinking('${messageId}')">
                            <div class="thinking-title">
                                <span class="thinking-icon">🧠</span>
                                <span>深度思考</span>
                            </div>
                            <span class="thinking-toggle" id="${messageId}-toggle">▼</span>
                        </div>
                        <div class="thinking-content" id="${messageId}-thinking">
${reasoningContent}</div>
                    </div>
                `;
            }
            
            // 添加答案部分
            if (answerContent) {
                html += `<div class="answer-section">`;
                if (isFinal && typeof marked !== 'undefined') {
                    // 最终渲染，使用Markdown
                    marked.setOptions({
                        breaks: true,
                        gfm: true,
                        highlight: function(code, lang) {
                            if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                                try {
                                    return hljs.highlight(code, { language: lang }).value;
                                } catch (err) {}
                            }
                            return code;
                        }
                    });
                    html += marked.parse(answerContent);
                } else {
                    html += answerContent.replace(/\n/g, '<br>');
                    if (!isFinal) {
                        html += '<span class="typing-cursor">▋</span>';
                    }
                }
                html += `</div>`;
            } else if (!isFinal) {
                html += '<span class="typing-cursor">▋</span>';
            }
            
            contentDiv.innerHTML = html;
            
            // 高亮代码块（最终渲染时）
            if (isFinal && typeof hljs !== 'undefined') {
                const codeBlocks = contentDiv.querySelectorAll('pre code');
                codeBlocks.forEach(block => {
                    hljs.highlightElement(block);
                });
            }
            
            // 滚动到底部
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 切换思考过程显示/隐藏
        function toggleThinking(messageId) {
            const thinkingContent = document.getElementById(`${messageId}-thinking`);
            const toggle = document.getElementById(`${messageId}-toggle`);
            
            if (!thinkingContent || !toggle) return;
            
            if (thinkingContent.classList.contains('expanded')) {
                thinkingContent.classList.remove('expanded');
                toggle.classList.remove('expanded');
                toggle.textContent = '▼';
            } else {
                thinkingContent.classList.add('expanded');
                toggle.classList.add('expanded');
                toggle.textContent = '▲';
            }
        }

        // 更新流式消息内容
        function updateStreamMessage(messageId, content, isFinal = false) {
            const messageElement = document.getElementById(messageId);
            if (!messageElement) return;
            
            const contentDiv = messageElement.querySelector('.message-content');
            if (!contentDiv) return;
            
            if (isFinal) {
                // 最终渲染，使用Markdown
                if (typeof marked !== 'undefined') {
                    // 配置marked选项
                    marked.setOptions({
                        breaks: true,
                        gfm: true,
                        highlight: function(code, lang) {
                            if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                                try {
                                    return hljs.highlight(code, { language: lang }).value;
                                } catch (err) {}
                            }
                            return code;
                        }
                    });
                    contentDiv.innerHTML = marked.parse(content);
                } else {
                    contentDiv.innerHTML = content.replace(/\n/g, '<br>');
                }
                
                // 高亮代码块
                if (typeof hljs !== 'undefined') {
                    const codeBlocks = contentDiv.querySelectorAll('pre code');
                    codeBlocks.forEach(block => {
                        hljs.highlightElement(block);
                    });
                }
            } else {
                // 流式更新，暂时显示纯文本
                contentDiv.innerHTML = content.replace(/\n/g, '<br>') + '<span class="typing-cursor">▋</span>';
            }
            
            // 滚动到底部
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 显示消息（支持Markdown渲染）
        function showMessage(content, role) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            if (role === 'assistant' && typeof marked !== 'undefined') {
                // AI回复使用Markdown渲染
                marked.setOptions({
                    breaks: true,
                    gfm: true,
                    highlight: function(code, lang) {
                        if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                            try {
                                return hljs.highlight(code, { language: lang }).value;
                            } catch (err) {}
                        }
                        return code;
                    }
                });
                contentDiv.innerHTML = marked.parse(content);
                
                // 高亮代码块
                if (typeof hljs !== 'undefined') {
                    const codeBlocks = contentDiv.querySelectorAll('pre code');
                    codeBlocks.forEach(block => {
                        hljs.highlightElement(block);
                    });
                }
            } else {
                // 用户消息使用纯文本
                contentDiv.textContent = content;
            }
            
            messageDiv.appendChild(contentDiv);
            messagesContainer.appendChild(messageDiv);
            
            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;        }        // 点击模态框外部关闭
        document.getElementById('configModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeConfig();
            }
        });

        // 网络状态监测
        function updateNetworkStatus() {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            
            if (navigator.onLine) {
                statusDot.className = 'status-dot';
                statusText.textContent = '在线';
                
                // 测试API连接
                fetch('https://api.deepseek.com/v1', { 
                    method: 'HEAD',
                    mode: 'no-cors'
                })
                .then(() => {
                    // 连接成功
                })
                .catch(() => {
                    // API可能有问题但仍然在线
                });
            } else {
                statusDot.className = 'status-dot offline';
                statusText.textContent = '离线';
            }
        }
        
        // 初始状态
        window.addEventListener('load', updateNetworkStatus);
        
        // 监听网络变化
        window.addEventListener('online', updateNetworkStatus);
        window.addEventListener('offline', updateNetworkStatus);
    </script>
</body>
</html>
