<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI课程助手</title>

    <!-- 外部依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.8.0/build/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.8.0/build/styles/github.min.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .model-selector {
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .model-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .model-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .model-btn.active {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            font-weight: 600;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            left: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #4CAF50;
        }

        .status-dot.online {
            background-color: #4CAF50;
        }

        .status-dot.offline {
            background-color: #F44336;
        }

        .status-dot.connecting {
            background-color: #FFC107;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .config-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .config-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            max-width: 80%;
            padding: 15px 20px;
            border-radius: 18px;
            line-height: 1.5;
            word-wrap: break-word;
        }

        .message.user {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            align-self: flex-end;
            margin-left: auto;
        }

        .message.assistant {
            background: #f8f9fa;
            color: #333;
            align-self: flex-start;
            border: 1px solid #e9ecef;
        }

        .message-content {
            font-size: 15px;
        }

        .message-content h1, .message-content h2, .message-content h3 {
            margin: 15px 0 10px 0;
            color: #2c3e50;
        }

        .message-content h1 { font-size: 1.5em; }
        .message-content h2 { font-size: 1.3em; }
        .message-content h3 { font-size: 1.1em; }

        .message-content p {
            margin: 10px 0;
        }

        .message-content ul, .message-content ol {
            margin: 10px 0;
            padding-left: 20px;
        }

        .message-content li {
            margin: 5px 0;
        }

        .message-content pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }

        .message-content code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .message-content pre code {
            background: none;
            padding: 0;
        }

        .message-content blockquote {
            border-left: 4px solid #667eea;
            margin: 15px 0;
            padding: 10px 20px;
            background: #f8f9fa;
            font-style: italic;
        }
        .thinking-section {
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .thinking-header {
            background: #f8f9fa;
            padding: 12px 15px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e9ecef;
            transition: background-color 0.3s ease;
        }

        .thinking-header:hover {
            background: #e9ecef;
        }

        .thinking-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #495057;
        }

        .thinking-icon {
            font-size: 16px;
        }

        .thinking-toggle {
            transition: transform 0.3s ease;
            font-size: 14px;
            color: #6c757d;
        }

        .thinking-toggle.expanded {
            transform: rotate(180deg);
        }

        .thinking-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #fff;
        }

        .thinking-content.expanded {
            max-height: 500px;
            padding: 15px;
            border-top: 1px solid #e9ecef;
        }

        .answer-section {
            margin-top: 10px;
        }

        .r1-response {
            border-left: 4px solid #667eea;
            padding-left: 15px;
        }

        .typing-cursor {
            animation: blink 1s infinite;
            color: #667eea;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .chat-input {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            background: white;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            padding: 12px 20px;
            font-size: 16px;
            resize: none;
            outline: none;
            transition: border-color 0.3s ease;
            max-height: 120px;
            min-height: 50px;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-btn:hover:not(:disabled) {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #333;
        }

        .config-group {
            margin-bottom: 20px;
        }

        .config-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .config-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .config-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .save-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }

        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 15px;
            }

            .chat-header {
                padding: 15px;
            }

            .chat-title {
                font-size: 20px;
            }

            .model-btn {
                padding: 6px 12px;
                font-size: 12px;
            }

            .message {
                max-width: 90%;
                padding: 12px 16px;
            }

            .status-indicator {
                font-size: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- 聊天头部 -->
        <div class="chat-header">
            <!-- 网络状态指示器 -->
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">检测中</span>
            </div>

            <div class="chat-title">🤖 AI课程助手</div>

            <!-- 模型选择器 -->
            <div class="model-selector">
                <button class="model-btn active" data-model="deepseek-v3">DeepSeek-V3</button>
                <button class="model-btn" data-model="deepseek-r1">DeepSeek-R1</button>
                <button class="model-btn" data-model="qwen">Qwen-R1</button>
                <button class="model-btn" data-model="qwen3-32b">Qwen3-32B</button>
            </div>

            <!-- API测试按钮 -->
            <button class="config-btn" onclick="testCurrentApiConnection()" title="测试当前模型API连接" style="right: 100px; font-size: 14px;">
                🔗
            </button>
            <!-- 测试按钮 -->
            <button class="config-btn" onclick="showTestThinkingProcess()" title="测试思考过程显示" style="right: 60px; font-size: 14px;">
                🧪
            </button>
            <!-- 配置按钮 -->
            <button class="config-btn" onclick="openConfig()" title="配置API密钥">
                ⚙️
            </button>
        </div>

        <!-- 聊天消息区域 -->
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-content">
                    👋 你好！我是AI课程助手，可以帮助你解答学习相关的问题。
                    <br><br>
                    💡 <strong>使用提示：</strong>
                    <br>• 选择上方的AI模型
                    <br>• 点击右上角⚙️配置API密钥
                    <br>• 输入你的问题开始对话
                    <br><br>
                    🚀 支持的功能：
                    <br>• 📚 课程内容解答
                    <br>• 💻 编程问题解决
                    <br>• 🧠 深度思考过程展示（R1模型）
                    <br>• 📝 Markdown格式输出
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input">
            <div class="input-container">
                <textarea
                    id="messageInput"
                    class="message-input"
                    placeholder="输入你的问题..."
                    onkeydown="handleKeyDown(event)"
                ></textarea>
                <button id="sendBtn" class="send-btn" onclick="sendMessage()">
                    ➤
                </button>
            </div>
        </div>
    </div>

    <!-- 配置模态框 -->
    <div id="configModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">API配置</h2>
                <span class="close" onclick="closeConfig()">&times;</span>
            </div>

            <div class="config-group">
                <label class="config-label" for="deepseekApiKey">DeepSeek API Key:</label>
                <input type="password" id="deepseekApiKey" class="config-input" placeholder="输入DeepSeek API密钥">
            </div>

            <div class="config-group">
                <label class="config-label" for="siliconflowApiKey">SiliconFlow API Key (Qwen-R1):</label>
                <input type="password" id="siliconflowApiKey" class="config-input" placeholder="输入SiliconFlow API密钥">
            </div>

            <div class="config-group">
                <label class="config-label" for="qwen3ApiKey">Qwen3-32B API Key:</label>
                <input type="password" id="qwen3ApiKey" class="config-input" placeholder="输入Qwen3-32B API密钥">
            </div>

            <button class="save-btn" onclick="saveConfig()">保存配置</button>
        </div>
    </div>

    <!-- 引入工具类和主应用 -->
    <script src="ai-utils.js"></script>
    <script src="ai-chat.js"></script>

    <script>
        // 全局函数，供HTML调用
        function openConfig() {
            window.app.openConfig();
        }

        function closeConfig() {
            window.app.closeConfig();
        }

        function saveConfig() {
            window.app.saveConfig();
        }

        function sendMessage() {
            window.app.sendMessage();
        }

        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                window.app.sendMessage();
            }
        }

        function showTestThinkingProcess() {
            window.app.showTestThinkingProcess();
        }

        function toggleThinking(messageId) {
            window.app.toggleThinking(messageId);
        }

        function testCurrentApiConnection() {
            window.app.testApiConnection();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，等待应用初始化...');
        });
    </script>
</body>
</html>
