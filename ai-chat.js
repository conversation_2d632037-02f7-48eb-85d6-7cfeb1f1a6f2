// AI聊天功能的主要逻辑
class AIChatApp {
    constructor() {
        this.currentModel = 'deepseek-v3';
        this.apiKeys = {
            deepseek: '',
            siliconflow: '',
            qwen3: ''
        };
        this.corsProxy = new CorsProxy();
        this.isGenerating = false;
        
        // 配置信息
        this.modelConfig = {
            'deepseek-v3': {
                name: 'DeepSeek-V3',
                apiUrl: 'https://api.deepseek.com/chat/completions',
                modelName: 'deepseek-chat',
                provider: 'deepseek'
            },
            'deepseek-r1': {
                name: 'DeepSeek-R1',
                apiUrl: 'https://api.deepseek.com/chat/completions',
                modelName: 'deepseek-reasoner',
                provider: 'deepseek'
            },
            'qwen': {
                name: 'DeepSeek-R1-Qwen-7B',
                apiUrl: 'https://api.siliconflow.cn/v1/chat/completions',
                modelName: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
                provider: 'siliconflow'
            },
            'qwen3-32b': {
                name: 'Qwen3-32B',
                apiUrl: 'https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1/chat/completions',
                modelName: 'qwen3:32b',
                provider: 'qwen3',
                useCorsProxy: true // 为Qwen3启用CORS代理
            }
        };
    }
    
    /**
     * 初始化应用
     */
    init() {
        this.loadConfig();
        this.setupModelSelector();
        this.setupTextareaAutoResize();
        this.setupKeyboardHandling();
        this.setupNetworkMonitoring();

        console.log('AI聊天应用初始化完成');
    }
    
    /**
     * 加载配置
     */
    loadConfig() {
        const config = localStorage.getItem('aiChatConfig');
        if (config) {
            try {
                const parsedConfig = JSON.parse(config);
                this.apiKeys = parsedConfig.apiKeys || this.apiKeys;
                
                // 填充配置表单
                document.getElementById('deepseekApiKey').value = this.apiKeys.deepseek || '';
                document.getElementById('siliconflowApiKey').value = this.apiKeys.siliconflow || '';
                document.getElementById('qwen3ApiKey').value = this.apiKeys.qwen3 || '';
            } catch (e) {
                console.error('配置加载失败:', e);
            }
        }
    }
    
    /**
     * 保存配置
     */
    saveConfig() {
        this.apiKeys.deepseek = document.getElementById('deepseekApiKey').value.trim();
        this.apiKeys.siliconflow = document.getElementById('siliconflowApiKey').value.trim();
        this.apiKeys.qwen3 = document.getElementById('qwen3ApiKey').value.trim();
        
        const config = {
            apiKeys: this.apiKeys
        };
        
        localStorage.setItem('aiChatConfig', JSON.stringify(config));
        this.closeConfig();
        
        // 显示保存成功消息
        this.showMessage('配置已保存！', 'assistant');
    }
    
    /**
     * 设置模型选择器
     */
    setupModelSelector() {
        const modelBtns = document.querySelectorAll('.model-btn');
        modelBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                modelBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentModel = btn.getAttribute('data-model');
            });
        });
    }
    
    /**
     * 设置文本框自动调整高度
     */
    setupTextareaAutoResize() {
        const textarea = document.getElementById('messageInput');
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });
    }
    
    /**
     * 设置键盘事件处理
     */
    setupKeyboardHandling() {
        const textarea = document.getElementById('messageInput');
        textarea.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                this.sendMessage();
            }
        });
    }

    /**
     * 设置网络监控
     */
    setupNetworkMonitoring() {
        // 监听在线/离线状态
        window.addEventListener('online', () => {
            console.log('🌐 网络已连接');
            this.corsProxy.updateNetworkStatus('online');
        });

        window.addEventListener('offline', () => {
            console.log('🚫 网络已断开');
            this.corsProxy.updateNetworkStatus('offline');
        });

        // 初始网络状态检测
        this.checkInitialNetworkStatus();
    }

    /**
     * 检查初始网络状态
     */
    async checkInitialNetworkStatus() {
        try {
            const isOnline = await this.corsProxy.testConnection();
            this.corsProxy.updateNetworkStatus(isOnline ? 'online' : 'offline');
        } catch (error) {
            console.warn('初始网络状态检测失败:', error.message);
            this.corsProxy.updateNetworkStatus('offline');
        }
    }
    
    /**
     * 打开配置模态框
     */
    openConfig() {
        document.getElementById('configModal').style.display = 'flex';
    }
    
    /**
     * 关闭配置模态框
     */
    closeConfig() {
        document.getElementById('configModal').style.display = 'none';
    }
    
    /**
     * 发送消息
     */
    async sendMessage() {
        // 如果正在生成响应，则忽略
        if (this.isGenerating) return;
        
        const input = document.getElementById('messageInput');
        const message = input.value.trim();
        
        if (!message) return;
        
        // 检查API密钥
        if (!this.checkApiKey()) {
            this.showMessage('请先配置API密钥！点击右上角⚙️进行配置。', 'assistant');
            return;
        }
        
        // 显示用户消息
        this.showMessage(message, 'user');
        input.value = '';
        input.style.height = 'auto';
        
        // 禁用发送按钮
        const sendBtn = document.getElementById('sendBtn');
        sendBtn.disabled = true;
        sendBtn.textContent = '发送中...';
        this.isGenerating = true;
        
        // 创建流式响应消息容器
        const messageId = 'stream-' + Date.now();
        this.createStreamMessage(messageId);
        
        try {
            await this.callAPIStream(message, messageId);
        } catch (error) {
            console.error('API调用错误:', error);

            // 提供更友好的错误信息
            let errorMessage = '抱歉，发生了错误：';

            if (error.message.includes('Failed to fetch')) {
                errorMessage += '\n\n🌐 **网络连接问题**\n' +
                              '- 请检查您的网络连接\n' +
                              '- 可能是API服务暂时不可用\n' +
                              '- 尝试切换到其他模型\n\n' +
                              '💡 **建议解决方案：**\n' +
                              '1. 刷新页面重试\n' +
                              '2. 检查API密钥是否正确\n' +
                              '3. 稍后再试';
            } else if (error.message.includes('401') || error.message.includes('403')) {
                errorMessage += '\n\n🔑 **API密钥问题**\n' +
                              '- API密钥可能无效或已过期\n' +
                              '- 请检查配置中的API密钥\n\n' +
                              '💡 **解决方案：**\n' +
                              '1. 点击右上角⚙️重新配置API密钥\n' +
                              '2. 确认密钥格式正确';
            } else if (error.message.includes('429')) {
                errorMessage += '\n\n⏰ **请求频率限制**\n' +
                              '- API调用过于频繁\n' +
                              '- 请稍等片刻再试\n\n' +
                              '💡 **建议：** 等待30秒后重试';
            } else {
                errorMessage += `\n\n❌ **错误详情：** ${error.message}\n\n` +
                              '💡 **建议：** 如果问题持续，请尝试刷新页面或联系技术支持';
            }

            this.updateStreamMessage(messageId, errorMessage, true);
        } finally {
            sendBtn.disabled = false;
            sendBtn.textContent = '发送';
            this.isGenerating = false;
        }
    }
    
    /**
     * 检查API密钥
     * @returns {boolean} - 是否有有效的API密钥
     */
    checkApiKey() {
        const model = this.modelConfig[this.currentModel];
        if (!model) return false;
        
        switch (model.provider) {
            case 'deepseek':
                return this.apiKeys.deepseek && this.apiKeys.deepseek.length > 0;
            case 'siliconflow':
                return this.apiKeys.siliconflow && this.apiKeys.siliconflow.length > 0;
            case 'qwen3':
                return this.apiKeys.qwen3 && this.apiKeys.qwen3.length > 0;
            default:
                return false;
        }
    }
    
    /**
     * 流式调用API
     * @param {string} message - 用户消息
     * @param {string} messageId - 消息ID
     */
    async callAPIStream(message, messageId) {
        const model = this.modelConfig[this.currentModel];
        if (!model) {
            throw new Error('未知的模型');
        }
        
        let apiKey;
        switch (model.provider) {
            case 'deepseek':
                apiKey = this.apiKeys.deepseek;
                break;
            case 'siliconflow':
                apiKey = this.apiKeys.siliconflow;
                break;
            case 'qwen3':
                apiKey = this.apiKeys.qwen3;
                break;
            default:
                throw new Error('未知的API提供商');
        }
        
        const headers = {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        };
        
        // 为Qwen3添加源头信息
        if (model.provider === 'qwen3') {
            headers['Origin'] = window.location.origin;
        }
        
        const body = {
            model: model.modelName,
            messages: [
                {
                    role: 'system',
                    content: '你是一个专业的课程助手，请帮助用户解答学习相关的问题。'
                },
                {
                    role: 'user',
                    content: message
                }
            ],
            stream: true,
            max_tokens: 2048,
            temperature: 0.7
        };
        
        // 为不同模型调整特定参数
        if (model.provider === 'siliconflow') {
            body.top_p = 0.7;
            body.top_k = 50;
            body.frequency_penalty = 0.5;
        } else if (model.provider === 'qwen3') {
            body.temperature = 0.6;
        }
        
        let response;
        if (model.useCorsProxy) {
            // 使用CORS代理，特别针对Qwen3-32B
            console.log('🔄 使用CORS代理调用API:', model.apiUrl);
            response = await this.corsProxy.fetch(model.apiUrl, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(body)
            });
        } else {
            // 直接调用API
            console.log('🌐 直接调用API:', model.apiUrl);
            response = await fetch(model.apiUrl, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(body),
                mode: 'cors',
                credentials: 'omit'
            });
        }
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`API错误 (${response.status}):`, errorText);
            throw new Error(`API请求失败 (${response.status}): ${errorText}`);
        }
        
        // 判断是否为支持思考过程的模型
        const supportsReasoning = ['deepseek-r1', 'qwen', 'qwen3-32b'].includes(this.currentModel);
        
        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        let fullContent = '';
        let reasoningContent = '';
        let answerContent = '';
        let hasDisplayedThinking = false;
        
        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                
                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';
                
                for (const line of lines) {
                    if (line.trim().startsWith('data: ')) {
                        const data = line.trim().slice(6);
                        if (data === '[DONE]') {
                            if (supportsReasoning && (reasoningContent || answerContent)) {
                                this.updateStreamMessageWithReasoning(messageId, reasoningContent, answerContent, true);
                            } else {
                                this.updateStreamMessage(messageId, fullContent, true);
                            }
                            return;
                        }
                        
                        try {
                            const chunk = JSON.parse(data);
                            
                            // 处理流式delta响应
                            if (chunk.choices && chunk.choices[0] && chunk.choices[0].delta) {
                                const delta = chunk.choices[0].delta;
                                
                                // 检查各种可能的思考内容字段
                                if (delta.reasoning_content) {
                                    reasoningContent += delta.reasoning_content;
                                    this.updateStreamMessageWithReasoning(messageId, reasoningContent, answerContent);
                                }
                                else if (delta.reasoning) {
                                    reasoningContent += delta.reasoning;
                                    this.updateStreamMessageWithReasoning(messageId, reasoningContent, answerContent);
                                }
                                else if (delta.content) {
                                    if (supportsReasoning) {
                                        answerContent += delta.content;
                                        
                                        // 检查内容中是否有思考标记
                                        const extracted = ThinkingExtractor.extract(answerContent);
                                        if (extracted.thinking && !hasDisplayedThinking) {
                                            reasoningContent = extracted.thinking;
                                            answerContent = extracted.answer;
                                            
                                            // 模拟流式显示思考过程
                                            this.simulateThinkingStream(messageId, reasoningContent, answerContent);
                                            hasDisplayedThinking = true;
                                            return;
                                        }
                                        
                                        this.updateStreamMessageWithReasoning(messageId, reasoningContent, answerContent);
                                    } else {
                                        fullContent += delta.content;
                                        this.updateStreamMessage(messageId, fullContent);
                                    }
                                }
                            }
                            // 处理完整的choice消息
                            else if (chunk.choices && chunk.choices[0] && chunk.choices[0].message) {
                                const message = chunk.choices[0].message;
                                
                                // 检查各种可能的思考内容
                                if (supportsReasoning && message.reasoning_content && !hasDisplayedThinking) {
                                    reasoningContent = message.reasoning_content;
                                    answerContent = message.content || '';
                                    
                                    // 模拟流式显示思考过程
                                    this.simulateThinkingStream(messageId, reasoningContent, answerContent);
                                    hasDisplayedThinking = true;
                                    return;
                                }
                                // 处理普通响应
                                else if (message.content) {
                                    if (supportsReasoning) {
                                        let content = message.content;
                                        
                                        // 尝试从内容中提取思考过程
                                        const extracted = ThinkingExtractor.extract(content);
                                        if (extracted.thinking && !hasDisplayedThinking) {
                                            reasoningContent = extracted.thinking;
                                            answerContent = extracted.answer;
                                            
                                            // 模拟流式显示思考过程
                                            this.simulateThinkingStream(messageId, reasoningContent, answerContent);
                                            hasDisplayedThinking = true;
                                            return;
                                        } else {
                                            answerContent = content;
                                            this.updateStreamMessageWithReasoning(messageId, reasoningContent, answerContent, true);
                                        }
                                    } else {
                                        fullContent = message.content;
                                        this.updateStreamMessage(messageId, fullContent, true);
                                    }
                                    return;
                                }
                            }
                        } catch (e) {
                            console.log('JSON解析错误:', e, 'Data:', data);
                        }
                    }
                }
            }
            
            // 确保最终渲染
            if (supportsReasoning && (reasoningContent || answerContent)) {
                // 最终检查是否有未处理的思考内容
                if (!hasDisplayedThinking) {
                    const extracted = ThinkingExtractor.extract(answerContent);
                    if (extracted.thinking) {
                        reasoningContent = extracted.thinking;
                        answerContent = extracted.answer;
                        this.simulateThinkingStream(messageId, reasoningContent, answerContent);
                        return;
                    }
                }
                this.updateStreamMessageWithReasoning(messageId, reasoningContent, answerContent, true);
            } else {
                this.updateStreamMessage(messageId, fullContent, true);
            }
            
        } finally {
            reader.releaseLock();
        }
    }
    
    /**
     * 模拟思考过程的流式显示
     * @param {string} messageId - 消息ID
     * @param {string} fullReasoning - 完整的思考过程
     * @param {string} finalAnswer - 最终答案
     */
    simulateThinkingStream(messageId, fullReasoning, finalAnswer) {
        if (!fullReasoning) {
            // 如果没有思考过程，直接显示答案
            this.updateStreamMessageWithReasoning(messageId, '', finalAnswer, true);
            return;
        }
        
        let currentReasoning = '';
        let currentAnswer = '';
        const reasoningChars = fullReasoning.split('');
        const answerChars = finalAnswer.split('');
        
        // 首先显示空的思考框架，并自动展开
        this.updateStreamMessageWithReasoning(messageId, '', '');
        setTimeout(() => {
            const thinkingContent = document.getElementById(`${messageId}-thinking`);
            const toggle = document.getElementById(`${messageId}-toggle`);
            if (thinkingContent && toggle) {
                thinkingContent.classList.add('expanded');
                toggle.classList.add('expanded');
                toggle.textContent = '▲';
            }
        }, 100);
        
        // 模拟思考过程的打字效果
        let reasoningIndex = 0;
        const thinkingInterval = setInterval(() => {
            if (reasoningIndex < reasoningChars.length) {
                currentReasoning += reasoningChars[reasoningIndex];
                reasoningIndex++;
                this.updateStreamMessageWithReasoning(messageId, currentReasoning, currentAnswer);
            } else {
                clearInterval(thinkingInterval);
                
                // 思考过程完成后，开始显示答案
                setTimeout(() => {
                    let answerIndex = 0;
                    const answerInterval = setInterval(() => {
                        if (answerIndex < answerChars.length) {
                            currentAnswer += answerChars[answerIndex];
                            answerIndex++;
                            this.updateStreamMessageWithReasoning(messageId, currentReasoning, currentAnswer);
                        } else {
                            clearInterval(answerInterval);
                            // 最终渲染
                            this.updateStreamMessageWithReasoning(messageId, currentReasoning, currentAnswer, true);
                        }
                    }, 20); // 答案显示速度稍快
                }, 500); // 思考完成后稍作停顿
            }
        }, 30); // 思考过程显示速度
    }
    
    /**
     * 创建流式消息容器
     * @param {string} messageId - 消息ID
     * @returns {HTMLElement} - 消息元素
     */
    createStreamMessage(messageId) {
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant';
        messageDiv.id = messageId;
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = '<span class="typing-cursor">▋</span>';
        
        messageDiv.appendChild(contentDiv);
        messagesContainer.appendChild(messageDiv);
        
        // 滚动到底部
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        return messageDiv;
    }
    
    /**
     * 更新带思考过程的流式消息内容
     * @param {string} messageId - 消息ID
     * @param {string} reasoningContent - 思考过程内容
     * @param {string} answerContent - 答案内容
     * @param {boolean} isFinal - 是否为最终渲染
     */
    updateStreamMessageWithReasoning(messageId, reasoningContent, answerContent, isFinal = false) {
        const messageElement = document.getElementById(messageId);
        if (!messageElement) return;
        
        const contentDiv = messageElement.querySelector('.message-content');
        if (!contentDiv) return;
        
        // 为支持思考过程的响应添加特殊样式类
        contentDiv.classList.add('r1-response');
        
        let html = '';
        
        // 如果有思考过程，添加思考部分
        if (reasoningContent) {
            html += `
                <div class="thinking-section">
                    <div class="thinking-header" onclick="app.toggleThinking('${messageId}')">
                        <div class="thinking-title">
                            <span class="thinking-icon">🧠</span>
                            <span>深度思考</span>
                        </div>
                        <span class="thinking-toggle" id="${messageId}-toggle">▼</span>
                    </div>
                    <div class="thinking-content" id="${messageId}-thinking">
${reasoningContent}</div>
                </div>
            `;
        }
        
        // 添加答案部分
        if (answerContent) {
            html += `<div class="answer-section">`;
            if (isFinal && typeof marked !== 'undefined') {
                // 最终渲染，使用Markdown
                marked.setOptions({
                    breaks: true,
                    gfm: true,
                    highlight: function(code, lang) {
                        if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                            try {
                                return hljs.highlight(code, { language: lang }).value;
                            } catch (err) {}
                        }
                        return code;
                    }
                });
                html += marked.parse(answerContent);
            } else {
                html += answerContent.replace(/\n/g, '<br>');
                if (!isFinal) {
                    html += '<span class="typing-cursor">▋</span>';
                }
            }
            html += `</div>`;
        } else if (!isFinal) {
            html += '<span class="typing-cursor">▋</span>';
        }
        
        contentDiv.innerHTML = html;
        
        // 高亮代码块（最终渲染时）
        if (isFinal && typeof hljs !== 'undefined') {
            const codeBlocks = contentDiv.querySelectorAll('pre code');
            codeBlocks.forEach(block => {
                hljs.highlightElement(block);
            });
        }
        
        // 滚动到底部
        const messagesContainer = document.getElementById('chatMessages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    /**
     * 切换思考过程显示/隐藏
     * @param {string} messageId - 消息ID
     */
    toggleThinking(messageId) {
        const thinkingContent = document.getElementById(`${messageId}-thinking`);
        const toggle = document.getElementById(`${messageId}-toggle`);
        
        if (!thinkingContent || !toggle) return;
        
        if (thinkingContent.classList.contains('expanded')) {
            thinkingContent.classList.remove('expanded');
            toggle.classList.remove('expanded');
            toggle.textContent = '▼';
        } else {
            thinkingContent.classList.add('expanded');
            toggle.classList.add('expanded');
            toggle.textContent = '▲';
        }
    }
    
    /**
     * 更新流式消息内容
     * @param {string} messageId - 消息ID
     * @param {string} content - 消息内容
     * @param {boolean} isFinal - 是否为最终渲染
     */
    updateStreamMessage(messageId, content, isFinal = false) {
        const messageElement = document.getElementById(messageId);
        if (!messageElement) return;
        
        const contentDiv = messageElement.querySelector('.message-content');
        if (!contentDiv) return;
        
        if (isFinal) {
            // 最终渲染，使用Markdown
            if (typeof marked !== 'undefined') {
                // 配置marked选项
                marked.setOptions({
                    breaks: true,
                    gfm: true,
                    highlight: function(code, lang) {
                        if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                            try {
                                return hljs.highlight(code, { language: lang }).value;
                            } catch (err) {}
                        }
                        return code;
                    }
                });
                contentDiv.innerHTML = marked.parse(content);
            } else {
                contentDiv.innerHTML = content.replace(/\n/g, '<br>');
            }
            
            // 高亮代码块
            if (typeof hljs !== 'undefined') {
                const codeBlocks = contentDiv.querySelectorAll('pre code');
                codeBlocks.forEach(block => {
                    hljs.highlightElement(block);
                });
            }
        } else {
            // 流式更新，暂时显示纯文本
            contentDiv.innerHTML = content.replace(/\n/g, '<br>') + '<span class="typing-cursor">▋</span>';
        }
        
        // 滚动到底部
        const messagesContainer = document.getElementById('chatMessages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    /**
     * 显示消息（支持Markdown渲染）
     * @param {string} content - 消息内容
     * @param {string} role - 消息角色（user或assistant）
     */
    showMessage(content, role) {
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        
        if (role === 'assistant' && typeof marked !== 'undefined') {
            // AI回复使用Markdown渲染
            marked.setOptions({
                breaks: true,
                gfm: true,
                highlight: function(code, lang) {
                    if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                        try {
                            return hljs.highlight(code, { language: lang }).value;
                        } catch (err) {}
                    }
                    return code;
                }
            });
            contentDiv.innerHTML = marked.parse(content);
            
            // 高亮代码块
            if (typeof hljs !== 'undefined') {
                const codeBlocks = contentDiv.querySelectorAll('pre code');
                codeBlocks.forEach(block => {
                    hljs.highlightElement(block);
                });
            }
        } else {
            // 用户消息使用纯文本
            contentDiv.textContent = content;
        }
        
        messageDiv.appendChild(contentDiv);
        messagesContainer.appendChild(messageDiv);
        
        // 滚动到底部
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    /**
     * 测试API连接
     * @param {string} modelKey - 模型键名
     */
    async testApiConnection(modelKey = null) {
        const testModel = modelKey || this.currentModel;
        const model = this.modelConfig[testModel];

        if (!model) {
            console.error('未知的模型:', testModel);
            return false;
        }

        console.log(`🧪 测试 ${model.name} API连接...`);

        try {
            // 构建测试请求
            const testMessage = '你好';
            const messageId = 'test-connection-' + Date.now();

            // 显示测试消息
            this.showMessage(`正在测试 ${model.name} API连接...`, 'assistant');

            await this.callAPIStream(testMessage, messageId);

            console.log(`✅ ${model.name} API连接测试成功`);
            this.showMessage(`✅ ${model.name} API连接正常！`, 'assistant');
            return true;

        } catch (error) {
            console.error(`❌ ${model.name} API连接测试失败:`, error);
            this.showMessage(`❌ ${model.name} API连接失败: ${error.message}`, 'assistant');
            return false;
        }
    }

    /**
     * 测试思考过程显示（开发用）
     */
    showTestThinkingProcess() {
        const messageId = 'test-' + Date.now();
        this.createStreamMessage(messageId);
        
        // 模拟思考过程数据
        const testReasoning = `我需要回答关于机器学习的问题。让我仔细思考一下：

1. 首先，我需要理解用户的问题层次
   - 这可能是一个基础概念问题
   - 用户可能需要通俗易懂的解释
   - 应该包含实际应用案例

2. 机器学习的核心要素：
   - 数据：机器学习的基础
   - 算法：处理数据的方法
   - 模型：从数据中学到的规律
   - 预测：应用模型解决新问题

3. 解释策略：
   - 用类比的方式帮助理解
   - 提供具体的实际应用例子
   - 避免过于技术性的术语
   - 结构化地组织信息

4. 应该涵盖的内容：
   - 定义和核心概念
   - 与传统编程的区别
   - 主要类型（监督、无监督、强化学习）
   - 实际应用场景
   - 简单的代码示例

让我现在组织一个清晰、易懂的回答。`;
        
        const testAnswer = `# 什么是机器学习？

**机器学习**（Machine Learning）是人工智能的一个重要分支，它让计算机能够从数据中自动学习规律和模式，并利用这些规律对新数据进行预测或决策，而无需人工明确编程每一个步骤。

## 🔍 核心理念

想象一下教小孩识别动物的过程：
- **传统编程**：我们告诉计算机"如果有四条腿、会汪汪叫，那就是狗"
- **机器学习**：我们给计算机看很多狗和猫的照片，让它自己总结规律

## 📚 主要类型

### 1. 监督学习 (Supervised Learning)
- 有"标准答案"的学习
- 例如：垃圾邮件分类、房价预测

### 2. 无监督学习 (Unsupervised Learning)  
- 自己发现数据中的规律
- 例如：客户群体分析、异常检测

### 3. 强化学习 (Reinforcement Learning)
- 通过试错和奖惩学习
- 例如：游戏AI、自动驾驶

## 🚀 实际应用

- 📧 **智能邮箱**：自动过滤垃圾邮件
- 🎵 **音乐推荐**：Spotify 的个性化播放列表  
- 🛒 **电商推荐**：Amazon 的"你可能喜欢"
- 🏥 **医疗诊断**：辅助医生分析医学影像
- 🚗 **自动驾驶**：特斯拉的自动驾驶系统
- 💬 **智能助手**：Siri、小爱同学

## 💻 简单示例

\`\`\`python
# 使用机器学习预测房价
from sklearn.linear_model import LinearRegression
import numpy as np

# 训练数据：房屋面积 -> 价格
X = np.array([[50], [80], [120], [150]])  # 面积(平米)
y = np.array([30, 50, 75, 95])           # 价格(万元)

# 创建和训练模型
model = LinearRegression()
model.fit(X, y)

# 预测100平米房子的价格
predicted_price = model.predict([[100]])
print(f"预测100平米房子价格: {predicted_price[0]:.1f}万元")
\`\`\`

## 🎯 为什么重要？

机器学习正在改变我们的生活方式，从个人助手到医疗诊断，从金融风控到交通出行。它让计算机具备了"学习"的能力，这是迈向真正智能系统的重要一步。

希望这个解释能帮助您理解机器学习的基本概念！有任何具体问题欢迎继续询问。`;
        
        // 使用新的流式显示函数
        setTimeout(() => {
            this.simulateThinkingStream(messageId, testReasoning, testAnswer);
        }, 500);
    }
}

// 创建全局实例
window.app = new AIChatApp();

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    window.app.init();
});
