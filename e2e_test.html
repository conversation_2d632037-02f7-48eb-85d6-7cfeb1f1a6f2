<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>端到端测试 - AI课程助手</title>
    <style>
        body {
            font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .card {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .test-summary {
            margin-top: 20px;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: 600;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        .failure {
            color: #F44336;
            font-weight: bold;
        }
        .button-container {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        button:hover {
            background: #3367d6;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
            padding: 8px 12px;
            border-radius: 4px;
            background-color: #f2f2f2;
            display: inline-flex;
        }
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #4CAF50;
        }
        .status-dot.offline {
            background-color: #F44336;
        }
        pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            font-size: 14px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .output {
            border-left: 4px solid #4285f4;
            margin-top: 10px;
        }
        .loading {
            display: none;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }
        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-top: 3px solid #4285f4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>AI课程助手 - 端到端测试</h1>
    
    <div class="card">
        <h2>网络状态</h2>
        <div class="status-indicator">
            <div class="status-dot" id="statusDot"></div>
            <span id="statusText">在线</span>
        </div>
        <p>提示: 在开发者工具的网络面板中切换"离线"模式来测试网络状态指示器</p>
    </div>
    
    <div class="card">
        <h2>端到端测试</h2>
        <p>此测试将检查以下功能:</p>
        <ul>
            <li>网络状态监测</li>
            <li>CORS代理功能</li>
            <li>思考过程提取</li>
            <li>四个模型配置</li>
        </ul>
        
        <div class="button-container">
            <button id="startTestButton" onclick="startTests()">开始端到端测试</button>
            <button id="clearButton" onclick="clearOutput()">清除输出</button>
        </div>
        
        <div class="loading" id="loadingIndicator">
            <div class="loading-spinner"></div>
            <span>测试中，请稍候...</span>
        </div>
        
        <pre id="testOutput" class="output">点击"开始端到端测试"按钮开始测试...</pre>
        
        <div class="test-summary" id="testSummary"></div>
    </div>
    
    <div class="card">
        <h2>聊天界面测试</h2>
        <p>点击下面的按钮在新窗口中打开聊天界面，测试实际使用场景：</p>
        <button onclick="window.open('ai-chat.html', '_blank')">打开聊天界面</button>
    </div>
    
    <!-- 加载工具库和测试脚本 -->
    <script src="ai-utils.js"></script>
    <script src="end_to_end_test.js"></script>
    
    <script>
        // 重定向控制台输出到页面
        function redirectConsoleToElement(elementId) {
            const originalConsoleLog = console.log;
            const originalConsoleError = console.error;
            const originalConsoleWarn = console.warn;
            const outputElement = document.getElementById(elementId);
            
            console.log = function() {
                const args = Array.from(arguments);
                outputElement.textContent += args.join(' ') + '\n';
                originalConsoleLog.apply(console, arguments);
                // 自动滚动到底部
                outputElement.scrollTop = outputElement.scrollHeight;
            };
            
            console.error = function() {
                const args = Array.from(arguments);
                outputElement.textContent += '错误: ' + args.join(' ') + '\n';
                originalConsoleError.apply(console, arguments);
                outputElement.scrollTop = outputElement.scrollHeight;
            };
            
            console.warn = function() {
                const args = Array.from(arguments);
                outputElement.textContent += '警告: ' + args.join(' ') + '\n';
                originalConsoleWarn.apply(console, arguments);
                outputElement.scrollTop = outputElement.scrollHeight;
            };
            
            return function() {
                console.log = originalConsoleLog;
                console.error = originalConsoleError;
                console.warn = originalConsoleWarn;
            };
        }
        
        // 开始测试
        async function startTests() {
            // 清除之前的结果
            clearOutput();
            
            // 显示加载中
            document.getElementById('loadingIndicator').style.display = 'flex';
            document.getElementById('startTestButton').disabled = true;
            
            const resetConsole = redirectConsoleToElement('testOutput');
            try {
                const tester = new EndToEndTester();
                await tester.runAllTests();
                
                // 显示测试摘要
                displayTestSummary(tester.getTestSummary());
            } catch (error) {
                console.error('测试执行失败:', error);
            } finally {
                resetConsole();
                // 隐藏加载中
                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('startTestButton').disabled = false;
            }
        }
        
        // 清除输出
        function clearOutput() {
            document.getElementById('testOutput').textContent = '';
            document.getElementById('testSummary').innerHTML = '';
        }
        
        // 显示测试摘要
        function displayTestSummary(summary) {
            const summaryElement = document.getElementById('testSummary');
            
            // 创建表格
            let tableHTML = '<h3>测试结果摘要</h3>';
            tableHTML += '<table>';
            tableHTML += '<tr><th>测试项目</th><th>结果</th></tr>';
            
            summary.forEach(item => {
                const isSuccess = item.结果.includes('✅');
                const resultClass = isSuccess ? 'success' : 'failure';
                tableHTML += `<tr><td>${item.测试项目}</td><td class="${resultClass}">${item.结果}</td></tr>`;
            });
            
            tableHTML += '</table>';
            summaryElement.innerHTML = tableHTML;
        }
        
        // 网络状态监测
        function updateNetworkStatus() {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            
            if (navigator.onLine) {
                statusDot.className = 'status-dot';
                statusText.textContent = '在线';
            } else {
                statusDot.className = 'status-dot offline';
                statusText.textContent = '离线';
            }
        }
        
        // 初始状态
        window.addEventListener('load', updateNetworkStatus);
        
        // 监听网络变化
        window.addEventListener('online', updateNetworkStatus);
        window.addEventListener('offline', updateNetworkStatus);
    </script>
</body>
</html>
