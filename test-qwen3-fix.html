<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qwen3-32B API 修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #5a6fd8;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>🔧 Qwen3-32B API 修复测试</h1>
    
    <div class="test-container">
        <h2>📋 测试说明</h2>
        <p>这个页面用于测试 Qwen3-32B API 的 CORS 问题修复效果。</p>
        <p><strong>使用方法：</strong></p>
        <ol>
            <li>输入您的 Qwen3-32B API 密钥</li>
            <li>点击测试按钮查看不同方法的效果</li>
            <li>查看控制台日志了解详细信息</li>
        </ol>
    </div>
    
    <div class="test-container">
        <h2>🔑 API 配置</h2>
        <label for="apiKey">Qwen3-32B API Key:</label><br>
        <input type="password" id="apiKey" placeholder="输入您的API密钥" style="width: 100%; padding: 8px; margin: 5px 0;">
    </div>
    
    <div class="test-container">
        <h2>🧪 测试方法</h2>
        <button class="test-button" onclick="testDirectFetch()">1. 直接请求测试</button>
        <button class="test-button" onclick="testCorsProxy()">2. CORS代理测试</button>
        <button class="test-button" onclick="testAllProxies()">3. 所有代理测试</button>
        <button class="test-button" onclick="testNetworkStatus()">4. 网络状态测试</button>
        
        <div id="testResult" class="test-result" style="display: none;"></div>
    </div>
    
    <div class="test-container">
        <h2>📊 测试结果</h2>
        <div id="resultSummary"></div>
    </div>

    <script src="ai-utils.js"></script>
    <script>
        let testResults = [];
        
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            
            // 添加到结果摘要
            testResults.push({ message, type, time: new Date().toLocaleTimeString() });
            updateResultSummary();
        }
        
        function updateResultSummary() {
            const summaryDiv = document.getElementById('resultSummary');
            summaryDiv.innerHTML = testResults.map(result => 
                `<div class="${result.type}" style="margin: 5px 0; padding: 5px; border-radius: 3px;">
                    [${result.time}] ${result.message}
                </div>`
            ).join('');
        }
        
        function getApiKey() {
            const apiKey = document.getElementById('apiKey').value.trim();
            if (!apiKey) {
                showResult('请先输入API密钥！', 'error');
                return null;
            }
            return apiKey;
        }
        
        async function testDirectFetch() {
            const apiKey = getApiKey();
            if (!apiKey) return;
            
            showResult('🌐 测试直接请求...', 'info');
            
            try {
                const response = await fetch('https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'qwen3:32b',
                        messages: [{ role: 'user', content: '你好' }],
                        max_tokens: 100
                    }),
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('✅ 直接请求成功！\n' + JSON.stringify(data, null, 2), 'success');
                } else {
                    showResult(`❌ 直接请求失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 直接请求错误: ${error.message}`, 'error');
            }
        }
        
        async function testCorsProxy() {
            const apiKey = getApiKey();
            if (!apiKey) return;
            
            showResult('🔄 测试CORS代理...', 'info');
            
            try {
                const corsProxy = new CorsProxy();
                const response = await corsProxy.fetch('https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'qwen3:32b',
                        messages: [{ role: 'user', content: '你好' }],
                        max_tokens: 100
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('✅ CORS代理请求成功！\n' + JSON.stringify(data, null, 2), 'success');
                } else {
                    showResult(`❌ CORS代理请求失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                showResult(`❌ CORS代理错误: ${error.message}`, 'error');
            }
        }
        
        async function testAllProxies() {
            const apiKey = getApiKey();
            if (!apiKey) return;
            
            showResult('🔄 测试所有代理方法...', 'info');
            
            const corsProxy = new CorsProxy();
            let successCount = 0;
            let totalCount = corsProxy.proxies.length;
            
            for (let i = 0; i < totalCount; i++) {
                const proxy = corsProxy.proxies[i];
                try {
                    showResult(`正在测试: ${proxy.name}...`, 'info');
                    
                    // 这里简化测试，只测试网络连接
                    const testUrl = proxy.type === 'direct' ? 
                        'https://httpbin.org/get' : 
                        proxy.url + encodeURIComponent('https://httpbin.org/get');
                    
                    const response = await fetch(testUrl, {
                        method: 'GET',
                        mode: 'cors'
                    });
                    
                    if (response.ok) {
                        successCount++;
                        showResult(`✅ ${proxy.name} 可用`, 'success');
                    } else {
                        showResult(`❌ ${proxy.name} 不可用: ${response.status}`, 'error');
                    }
                } catch (error) {
                    showResult(`❌ ${proxy.name} 错误: ${error.message}`, 'error');
                }
                
                corsProxy.rotateProxy();
            }
            
            showResult(`📊 代理测试完成: ${successCount}/${totalCount} 个代理可用`, 
                      successCount > 0 ? 'success' : 'error');
        }
        
        async function testNetworkStatus() {
            showResult('🌐 测试网络状态...', 'info');
            
            try {
                const corsProxy = new CorsProxy();
                const isOnline = await corsProxy.testConnection();
                
                showResult(`网络状态: ${isOnline ? '✅ 在线' : '❌ 离线'}`, 
                          isOnline ? 'success' : 'error');
            } catch (error) {
                showResult(`网络测试错误: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            showResult('🚀 测试页面已加载，请开始测试', 'info');
        });
    </script>
</body>
</html>
