// 浏览器fetch API的CORS代理工具
// 用于解决跨域问题，特别是对于Qwen3-32B API调用
class CorsProxy {
    constructor() {
        this.proxyUrl = 'https://corsproxy.io/?';
        this.fallbackProxies = [
            'https://cors-anywhere.herokuapp.com/',
            'https://api.allorigins.win/raw?url='
        ];
        this.currentProxyIndex = 0;
    }

    /**
     * 通过代理包装fetch请求
     * @param {string} url - 原始URL
     * @param {Object} options - fetch选项
     * @returns {Promise} - fetch响应
     */
    async fetch(url, options = {}) {
        console.log('使用CORS代理发送请求:', url);
        
        // 增加重试逻辑
        let retryCount = 0;
        const maxRetries = 3;
        
        while (retryCount < maxRetries) {
            try {
                // 对于OPTIONS预检请求不使用代理
                if (options.method === 'OPTIONS') {
                    return await fetch(url, options);
                }
                
                // 优先尝试不使用代理的请求
                if (retryCount === 0) {
                    try {
                        console.log('尝试直接请求（无代理）');
                        const directResponse = await fetch(url, {
                            ...options,
                            mode: 'cors',
                            headers: {
                                ...options.headers,
                                'Origin': window.location.origin
                            }
                        });
                        
                        if (directResponse.ok) {
                            console.log('直接请求成功！');
                            return directResponse;
                        }
                    } catch (directError) {
                        console.log('直接请求失败，将使用代理:', directError.message);
                    }
                }
                
                // 选择当前代理
                const currentProxy = this.getCurrentProxy();
                const proxyUrl = currentProxy + encodeURIComponent(url);
                
                // 确保headers存在
                options.headers = options.headers || {};
                
                // 添加额外的CORS相关headers
                options.headers['X-Requested-With'] = 'XMLHttpRequest';
                
                // 如果是POST请求确保正确的Content-Type
                if (options.method === 'POST' && !options.headers['Content-Type']) {
                    options.headers['Content-Type'] = 'application/json';
                }
                
                console.log(`使用代理 ${this.currentProxyIndex + 1}/${this.fallbackProxies.length + 1}: ${currentProxy}`);
                const response = await fetch(proxyUrl, options);
                
                // 检查响应状态
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error(`API请求失败 (${response.status}):`, errorText);
                    throw new Error(`状态码: ${response.status}, 错误: ${errorText}`);
                }
                
                return response;
            } catch (error) {
                retryCount++;
                console.warn(`请求失败(尝试 ${retryCount}/${maxRetries}):`, error.message);
                
                // 尝试切换到下一个代理
                this.rotateProxy();
                
                if (retryCount >= maxRetries) {
                    console.error('达到最大重试次数，请求失败');
                    throw error;
                }
                
                // 等待一段时间再重试
                await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
            }
        }
    }
    
    /**
     * 获取当前代理URL
     * @returns {string} 当前代理URL
     */
    getCurrentProxy() {
        if (this.currentProxyIndex === 0) {
            return this.proxyUrl;
        } else {
            return this.fallbackProxies[this.currentProxyIndex - 1];
        }
    }
    
    /**
     * 轮换到下一个可用的代理
     */
    rotateProxy() {
        this.currentProxyIndex = (this.currentProxyIndex + 1) % (this.fallbackProxies.length + 1);
        console.log(`切换到代理 ${this.currentProxyIndex + 1}/${this.fallbackProxies.length + 1}`);
    }
}

// 思考过程提取工具
class ThinkingExtractor {
    /**
     * 提取思考过程内容
     * @param {string} content - AI响应内容
     * @returns {Object} - 包含思考过程和答案的对象
     */
    static extract(content) {
        if (!content) return { thinking: '', answer: '' };
        
        let thinking = '';
        let answer = content;
        
        // 各种可能的思考标记格式
        const patterns = [
            // Qwen3-32B格式
            { pattern: /<think>([\s\S]*?)<\/think>/g, replacement: '<think>$1</think>' },
            // DeepSeek-R1格式
            { pattern: /<\|thinking\|>([\s\S]*?)<\|\/thinking\|>/g, replacement: '<|thinking|>$1</|thinking|>' },
            // ChatGLM风格标记
            { pattern: /<思考>([\s\S]*?)<\/思考>/g, replacement: '<思考>$1</思考>' },
            // 其他可能的格式
            { pattern: /\[思考开始\]([\s\S]*?)\[思考结束\]/g, replacement: '[思考开始]$1[思考结束]' },
            { pattern: /\[Thinking\]([\s\S]*?)\[\/Thinking\]/g, replacement: '[Thinking]$1[/Thinking]' },
            { pattern: /\[REASONING\]([\s\S]*?)\[\/REASONING\]/g, replacement: '[REASONING]$1[/REASONING]' },
            { pattern: /\{\{思考\}\}([\s\S]*?)\{\{\/思考\}\}/g, replacement: '{{思考}}$1{{/思考}}' }
        ];
        
        for (const { pattern, replacement } of patterns) {
            const matches = [...content.matchAll(pattern)];
            if (matches.length > 0) {
                // 提取所有匹配的思考内容
                matches.forEach(match => {
                    thinking += match[1].trim() + '\n\n';
                    answer = answer.replace(match[0], '').trim();
                });
                
                // 找到匹配后就停止
                break;
            }
        }
        
        // 如果没有找到标记的思考过程，尝试启发式检测
        if (!thinking) {
            // 检查是否包含"让我思考"、"步骤分析"等关键段落
            const heuristicPatterns = [
                /(?:让我思考一下|我来分析一下|首先思考|让我们来分析|分析步骤如下|思考过程)[：:]([\s\S]*?)(?=\n\n|$)/i,
                /(?:Step\s*1:|步骤\s*1:|第一步:)([\s\S]*?)(?=\n\n|$)/i,
            ];
            
            for (const pattern of heuristicPatterns) {
                const match = content.match(pattern);
                if (match) {
                    // 找到可能的思考内容
                    thinking = match[0];
                    answer = content.replace(thinking, '').trim();
                    break;
                }
            }
        }
        
        return {
            thinking: thinking.trim(),
            answer: answer.trim()
        };
    }
    
    /**
     * 测试思考提取功能
     * @param {string} testContent - 测试内容
     * @returns {Object} - 测试结果
     */
    static test(testContent) {
        console.log('测试思考内容提取...');
        console.log('输入长度:', testContent.length);
        
        const start = performance.now();
        const result = this.extract(testContent);
        const duration = performance.now() - start;
        
        console.log('提取耗时:', duration.toFixed(2), 'ms');
        console.log('思考内容长度:', result.thinking.length);
        console.log('答案内容长度:', result.answer.length);
        
        return result;
    }
}

// 导出工具类
window.CorsProxy = CorsProxy;
window.ThinkingExtractor = ThinkingExtractor;
