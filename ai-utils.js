// 浏览器fetch API的CORS代理工具
// 用于解决跨域问题，特别是对于Qwen3-32B API调用
class CorsProxy {
    constructor() {
        // 更新更可靠的代理服务列表
        this.proxies = [
            // 无代理直接请求（优先尝试）
            { url: '', name: '直接请求', type: 'direct' },
            // 公共CORS代理服务
            { url: 'https://cors-anywhere.herokuapp.com/', name: 'CORS Anywhere', type: 'prefix' },
            { url: 'https://api.allorigins.win/raw?url=', name: 'AllOrigins', type: 'prefix' },
            { url: 'https://corsproxy.io/?', name: 'CorsProxy.io', type: 'prefix' },
            { url: 'https://cors.bridged.cc/', name: 'Bridged CORS', type: 'prefix' },
            { url: 'https://thingproxy.freeboard.io/fetch/', name: 'ThingProxy', type: 'prefix' }
        ];
        this.currentProxyIndex = 0;
        this.maxRetries = this.proxies.length;
    }

    /**
     * 通过代理包装fetch请求
     * @param {string} url - 原始URL
     * @param {Object} options - fetch选项
     * @returns {Promise} - fetch响应
     */
    async fetch(url, options = {}) {
        console.log('🌐 开始API请求:', url);

        // 更新网络状态
        this.updateNetworkStatus('connecting');

        let lastError = null;

        // 尝试所有可用的代理
        for (let i = 0; i < this.proxies.length; i++) {
            const proxy = this.proxies[this.currentProxyIndex];

            try {
                console.log(`🔄 尝试 ${proxy.name} (${this.currentProxyIndex + 1}/${this.proxies.length})`);

                let requestUrl = url;
                let requestOptions = { ...options };

                // 根据代理类型构建请求
                if (proxy.type === 'direct') {
                    // 直接请求，添加必要的CORS头
                    requestOptions.mode = 'cors';
                    requestOptions.credentials = 'omit';
                    requestOptions.headers = {
                        ...requestOptions.headers,
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
                    };
                } else if (proxy.type === 'prefix') {
                    // 使用代理前缀
                    requestUrl = proxy.url + encodeURIComponent(url);

                    // 为代理请求优化headers
                    requestOptions.headers = {
                        ...requestOptions.headers,
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-Cors-Api-Key': 'temp_key_for_demo'
                    };

                    // 移除可能导致问题的headers
                    delete requestOptions.headers['Origin'];
                }

                // 设置超时
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时
                requestOptions.signal = controller.signal;

                const response = await fetch(requestUrl, requestOptions);
                clearTimeout(timeoutId);

                if (response.ok) {
                    console.log(`✅ ${proxy.name} 请求成功!`);
                    this.updateNetworkStatus('online');
                    return response;
                } else {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

            } catch (error) {
                lastError = error;
                console.warn(`❌ ${proxy.name} 失败:`, error.message);

                // 切换到下一个代理
                this.rotateProxy();

                // 如果不是最后一次尝试，等待一下再继续
                if (i < this.proxies.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }
        }

        // 所有代理都失败了
        console.error('🚫 所有代理都失败了，最后错误:', lastError?.message);
        this.updateNetworkStatus('offline');
        throw new Error(`网络请求失败: ${lastError?.message || '未知错误'}`);
    }
    
    /**
     * 轮换到下一个可用的代理
     */
    rotateProxy() {
        this.currentProxyIndex = (this.currentProxyIndex + 1) % this.proxies.length;
        console.log(`🔄 切换到代理: ${this.proxies[this.currentProxyIndex].name}`);
    }

    /**
     * 更新网络状态指示器
     * @param {string} status - 状态: 'online', 'offline', 'connecting'
     */
    updateNetworkStatus(status) {
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');

        if (statusDot && statusText) {
            statusDot.className = 'status-dot';

            switch (status) {
                case 'online':
                    statusDot.classList.add('online');
                    statusText.textContent = '在线';
                    break;
                case 'offline':
                    statusDot.classList.add('offline');
                    statusText.textContent = '离线';
                    break;
                case 'connecting':
                    statusDot.classList.add('connecting');
                    statusText.textContent = '连接中';
                    break;
            }
        }
    }

    /**
     * 测试网络连接
     * @returns {Promise<boolean>} - 连接是否成功
     */
    async testConnection() {
        try {
            const response = await fetch('https://httpbin.org/get', {
                method: 'GET',
                mode: 'cors',
                cache: 'no-cache'
            });
            return response.ok;
        } catch (error) {
            console.warn('网络连接测试失败:', error.message);
            return false;
        }
    }
}

// 思考过程提取工具
class ThinkingExtractor {
    /**
     * 提取思考过程内容
     * @param {string} content - AI响应内容
     * @returns {Object} - 包含思考过程和答案的对象
     */
    static extract(content) {
        if (!content) return { thinking: '', answer: '' };
        
        let thinking = '';
        let answer = content;
        
        // 各种可能的思考标记格式
        const patterns = [
            // Qwen3-32B格式
            { pattern: /<think>([\s\S]*?)<\/think>/g, replacement: '<think>$1</think>' },
            // DeepSeek-R1格式
            { pattern: /<\|thinking\|>([\s\S]*?)<\|\/thinking\|>/g, replacement: '<|thinking|>$1</|thinking|>' },
            // ChatGLM风格标记
            { pattern: /<思考>([\s\S]*?)<\/思考>/g, replacement: '<思考>$1</思考>' },
            // 其他可能的格式
            { pattern: /\[思考开始\]([\s\S]*?)\[思考结束\]/g, replacement: '[思考开始]$1[思考结束]' },
            { pattern: /\[Thinking\]([\s\S]*?)\[\/Thinking\]/g, replacement: '[Thinking]$1[/Thinking]' },
            { pattern: /\[REASONING\]([\s\S]*?)\[\/REASONING\]/g, replacement: '[REASONING]$1[/REASONING]' },
            { pattern: /\{\{思考\}\}([\s\S]*?)\{\{\/思考\}\}/g, replacement: '{{思考}}$1{{/思考}}' }
        ];
        
        for (const { pattern } of patterns) {
            const matches = [...content.matchAll(pattern)];
            if (matches.length > 0) {
                // 提取所有匹配的思考内容
                matches.forEach(match => {
                    thinking += match[1].trim() + '\n\n';
                    answer = answer.replace(match[0], '').trim();
                });

                // 找到匹配后就停止
                break;
            }
        }
        
        // 如果没有找到标记的思考过程，尝试启发式检测
        if (!thinking) {
            // 检查是否包含"让我思考"、"步骤分析"等关键段落
            const heuristicPatterns = [
                /(?:让我思考一下|我来分析一下|首先思考|让我们来分析|分析步骤如下|思考过程)[：:]([\s\S]*?)(?=\n\n|$)/i,
                /(?:Step\s*1:|步骤\s*1:|第一步:)([\s\S]*?)(?=\n\n|$)/i,
            ];
            
            for (const pattern of heuristicPatterns) {
                const match = content.match(pattern);
                if (match) {
                    // 找到可能的思考内容
                    thinking = match[0];
                    answer = content.replace(thinking, '').trim();
                    break;
                }
            }
        }
        
        return {
            thinking: thinking.trim(),
            answer: answer.trim()
        };
    }
    
    /**
     * 测试思考提取功能
     * @param {string} testContent - 测试内容
     * @returns {Object} - 测试结果
     */
    static test(testContent) {
        console.log('测试思考内容提取...');
        console.log('输入长度:', testContent.length);
        
        const start = performance.now();
        const result = this.extract(testContent);
        const duration = performance.now() - start;
        
        console.log('提取耗时:', duration.toFixed(2), 'ms');
        console.log('思考内容长度:', result.thinking.length);
        console.log('答案内容长度:', result.answer.length);
        
        return result;
    }
}

// Qwen3-32B 专用API调用工具
class Qwen3ApiHelper {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseUrl = 'https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1';
    }

    /**
     * 使用JSONP方式调用API（备用方案）
     * @param {Object} requestData - 请求数据
     * @returns {Promise} - 响应Promise
     */
    async callWithJsonp(requestData) {
        return new Promise((resolve, reject) => {
            // 创建JSONP回调函数
            const callbackName = 'qwen3_callback_' + Date.now();
            window[callbackName] = function(data) {
                delete window[callbackName];
                document.head.removeChild(script);
                resolve(data);
            };

            // 创建script标签
            const script = document.createElement('script');
            const params = new URLSearchParams({
                callback: callbackName,
                data: JSON.stringify(requestData)
            });

            script.src = `${this.baseUrl}/chat/completions?${params}`;
            script.onerror = () => {
                delete window[callbackName];
                document.head.removeChild(script);
                reject(new Error('JSONP请求失败'));
            };

            document.head.appendChild(script);

            // 设置超时
            setTimeout(() => {
                if (window[callbackName]) {
                    delete window[callbackName];
                    document.head.removeChild(script);
                    reject(new Error('JSONP请求超时'));
                }
            }, 30000);
        });
    }

    /**
     * 使用iframe方式调用API（另一个备用方案）
     * @param {Object} requestData - 请求数据
     * @returns {Promise} - 响应Promise
     */
    async callWithIframe(requestData) {
        return new Promise((resolve, reject) => {
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';

            // 创建表单数据
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `${this.baseUrl}/chat/completions`;
            form.target = iframe.name = 'qwen3_frame_' + Date.now();

            // 添加请求数据
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'data';
            input.value = JSON.stringify(requestData);
            form.appendChild(input);

            // 添加API密钥
            const keyInput = document.createElement('input');
            keyInput.type = 'hidden';
            keyInput.name = 'authorization';
            keyInput.value = `Bearer ${this.apiKey}`;
            form.appendChild(keyInput);

            iframe.onload = () => {
                try {
                    const response = iframe.contentDocument.body.textContent;
                    const data = JSON.parse(response);
                    document.body.removeChild(iframe);
                    document.body.removeChild(form);
                    resolve(data);
                } catch (error) {
                    document.body.removeChild(iframe);
                    document.body.removeChild(form);
                    reject(error);
                }
            };

            iframe.onerror = () => {
                document.body.removeChild(iframe);
                document.body.removeChild(form);
                reject(new Error('iframe请求失败'));
            };

            document.body.appendChild(iframe);
            document.body.appendChild(form);
            form.submit();

            // 设置超时
            setTimeout(() => {
                if (document.body.contains(iframe)) {
                    document.body.removeChild(iframe);
                    document.body.removeChild(form);
                    reject(new Error('iframe请求超时'));
                }
            }, 30000);
        });
    }
}

// 导出工具类
window.CorsProxy = CorsProxy;
window.ThinkingExtractor = ThinkingExtractor;
window.Qwen3ApiHelper = Qwen3ApiHelper;
