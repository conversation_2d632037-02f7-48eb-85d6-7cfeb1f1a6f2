<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试 - AI课程助手</title>
    <style>
        body {
            font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .card {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #3367d6;
        }
        pre {
            background: #f1f1f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 400px;
            overflow-y: auto;
        }
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 10px;
        }
        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #4CAF50;
        }
        .status-dot.offline {
            background-color: #F44336;
        }
    </style>
</head>
<body>
    <h1>AI课程助手 - 功能测试</h1>
    
    <div class="card">
        <h2>网络状态</h2>
        <div class="status-indicator">
            <div class="status-dot" id="statusDot"></div>
            <span id="statusText">在线</span>
        </div>
        <p>提示: 在开发者工具的网络面板中切换"离线"模式来测试网络状态指示器</p>
    </div>
    
    <div class="card">
        <h2>CORS代理测试</h2>
        <button onclick="testCorsProxy()">测试CORS代理</button>
        <pre id="corsOutput">点击按钮开始测试...</pre>
    </div>
    
    <div class="card">
        <h2>思考过程提取测试</h2>
        <button onclick="testThinkingExtractor()">测试思考提取</button>
        <pre id="thinkingOutput">点击按钮开始测试...</pre>
    </div>
    
    <div class="card">
        <h2>Qwen3-32B API测试</h2>
        <button onclick="testQwen3API()">测试Qwen3 API</button>
        <pre id="apiOutput">点击按钮开始测试...</pre>
    </div>
    
    <!-- 加载工具库和测试脚本 -->
    <script src="ai-utils.js"></script>
    <script src="test_features.js"></script>
    
    <script>
        // 网络状态监测
        function updateNetworkStatus() {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            
            if (navigator.onLine) {
                statusDot.className = 'status-dot';
                statusText.textContent = '在线';
            } else {
                statusDot.className = 'status-dot offline';
                statusText.textContent = '离线';
            }
        }
        
        // 初始状态
        window.addEventListener('load', updateNetworkStatus);
        
        // 监听网络变化
        window.addEventListener('online', updateNetworkStatus);
        window.addEventListener('offline', updateNetworkStatus);
        
        // 重定向控制台输出到页面
        function redirectConsoleToElement(elementId) {
            const originalConsoleLog = console.log;
            const originalConsoleError = console.error;
            const originalConsoleWarn = console.warn;
            const outputElement = document.getElementById(elementId);
            
            outputElement.textContent = '';
            
            console.log = function() {
                const args = Array.from(arguments);
                outputElement.textContent += args.join(' ') + '\n';
                originalConsoleLog.apply(console, arguments);
            };
            
            console.error = function() {
                const args = Array.from(arguments);
                outputElement.textContent += '错误: ' + args.join(' ') + '\n';
                originalConsoleError.apply(console, arguments);
            };
            
            console.warn = function() {
                const args = Array.from(arguments);
                outputElement.textContent += '警告: ' + args.join(' ') + '\n';
                originalConsoleWarn.apply(console, arguments);
            };
            
            return function() {
                console.log = originalConsoleLog;
                console.error = originalConsoleError;
                console.warn = originalConsoleWarn;
            };
        }
        
        // 测试CORS代理
        async function testCorsProxy() {
            const resetConsole = redirectConsoleToElement('corsOutput');
            try {
                const corsProxy = new CorsProxy();
                
                console.log('=== 测试CORS代理功能 ===');
                
                // 测试API端点
                const testUrls = [
                    'https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1/models', // Qwen3-32B API
                    'https://api.deepseek.com/v1/models' // DeepSeek API
                ];
                
                for (const url of testUrls) {
                    console.log(`\n测试URL: ${url}`);
                    try {
                        // 仅测试HEAD请求以避免API消耗
                        const response = await corsProxy.fetch(url, { 
                            method: 'HEAD',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        
                        console.log(`状态: ${response.status} ${response.statusText}`);
                        console.log(`代理: ${corsProxy.getCurrentProxy()}`);
                        console.log('测试成功 ✅');
                    } catch (error) {
                        console.error(`测试失败 ❌: ${error.message}`);
                        console.log(`当前代理: ${corsProxy.getCurrentProxy()}`);
                    }
                }
                
                // 测试代理轮换
                console.log('\n测试代理轮换功能:');
                const initialProxy = corsProxy.getCurrentProxy();
                console.log(`初始代理: ${initialProxy}`);
                
                corsProxy.rotateProxy();
                const secondProxy = corsProxy.getCurrentProxy();
                console.log(`轮换后代理: ${secondProxy}`);
                
                corsProxy.rotateProxy();
                const thirdProxy = corsProxy.getCurrentProxy();
                console.log(`再次轮换后代理: ${thirdProxy}`);
            } catch (error) {
                console.error('测试过程中发生错误:', error);
            } finally {
                resetConsole();
            }
        }
        
        // 测试思考过程提取
        function testThinkingExtractor() {
            const resetConsole = redirectConsoleToElement('thinkingOutput');
            try {
                console.log('=== 测试思考过程提取功能 ===');
                
                // 测试不同格式的思考过程
                const testCases = [
                    {
                        name: 'Qwen3-32B格式',
                        content: '让我思考一下这个问题。<think>机器学习是人工智能的一个子领域，主要关注如何让计算机从数据中学习。它使用统计学方法使计算机在没有被明确编程的情况下进行预测或决策。</think>机器学习是一种让计算机能够从数据中学习并做出决策的方法，无需显式编程。'
                    },
                    {
                        name: 'DeepSeek-R1格式',
                        content: '让我分析一下。<|thinking|>机器学习有三种主要类型：监督学习、无监督学习和强化学习。监督学习使用标记数据，无监督学习处理未标记数据，强化学习通过与环境交互来学习。</|thinking|>机器学习主要分为三类：监督学习、无监督学习和强化学习。'
                    },
                    {
                        name: 'ChatGLM风格',
                        content: '<思考>深度学习是机器学习的一个分支，基于人工神经网络。它使用多层神经网络来模拟人脑的学习过程，可以处理更复杂的模式识别任务。</思考>深度学习是机器学习的一个分支，使用多层神经网络模拟人脑学习过程。'
                    },
                    {
                        name: '自定义标记格式',
                        content: '[思考开始]卷积神经网络(CNN)主要用于图像处理，而循环神经网络(RNN)更适合序列数据如文本和语音。[思考结束]不同类型的神经网络适用于不同类型的数据。'
                    },
                    {
                        name: '无标记的思考内容',
                        content: '让我思考一下这个问题：决策树是一种基于树状结构进行决策的监督学习算法。它通过一系列问题将数据集分割成越来越小的子集，直到每个叶节点对应一个类别。\n\n决策树是一种直观的机器学习算法，通过问题序列将数据分类。'
                    }
                ];
                
                for (const testCase of testCases) {
                    console.log(`\n测试: ${testCase.name}`);
                    const result = ThinkingExtractor.extract(testCase.content);
                    
                    console.log(`思考内容长度: ${result.thinking.length}`);
                    console.log(`回答内容长度: ${result.answer.length}`);
                    
                    if (result.thinking) {
                        console.log(`思考内容预览: ${result.thinking.substring(0, 50)}...`);
                        console.log('测试成功 ✅');
                    } else {
                        console.log('未提取到思考内容 ❌');
                    }
                }
            } catch (error) {
                console.error('测试过程中发生错误:', error);
            } finally {
                resetConsole();
            }
        }
        
        // 测试Qwen3-32B API
        async function testQwen3API() {
            const resetConsole = redirectConsoleToElement('apiOutput');
            try {
                console.log('=== 测试Qwen3-32B API ===');
                console.log('启动API测试，这可能需要几秒钟...');
                
                const API_URL = 'https://api-wdf2x6i7w7u3ebj8.aistudio-app.com/v1/chat/completions';
                const API_KEY = '0621faa5641beacc5940d0fb978114a03a69b7eb';
                const corsProxy = new CorsProxy();
                
                console.log('使用CORS代理发送请求...');
                
                const response = await corsProxy.fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'qwen3:32b',
                        messages: [
                            {
                                role: 'system',
                                content: '你是一个专业的课程助手，请帮助用户解答学习相关的问题。'
                            },
                            {
                                role: 'user',
                                content: '什么是机器学习？请简单解释一下。'
                            }
                        ],
                        max_tokens: 100,
                        temperature: 0.7
                    })
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error(`API请求失败 (${response.status}): ${errorText}`);
                    return;
                }
                
                const data = await response.json();
                console.log('API请求成功！');
                console.log('响应内容:', JSON.stringify(data, null, 2));
                
                if (data.choices && data.choices[0] && data.choices[0].message) {
                    const content = data.choices[0].message.content;
                    console.log('\n回答内容:');
                    console.log(content);
                    
                    // 尝试提取思考过程
                    const extracted = ThinkingExtractor.extract(content);
                    if (extracted.thinking) {
                        console.log('\n提取到的思考过程:');
                        console.log(extracted.thinking);
                    }
                }
            } catch (error) {
                console.error('测试失败:', error.message);
            } finally {
                resetConsole();
            }
        }
    </script>
</body>
</html>
